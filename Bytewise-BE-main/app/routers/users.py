from fastapi import APIRouter, Depends, HTTPException
from ..dependencies import authenticate_user
from supabase import create_client, Client
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["users"],
)


@router.get("/user-info")
async def get_user_info(user_id: str = Depends(authenticate_user)):
    response = supabase.from_("users").select("user_id, username, full_name, email, role_name, personnel_id").eq("user_id", user_id).execute()
    if not response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_data = response.data[0]
    return user_data


@router.get("/protected-route")
async def protected_route(user_id: str = Depends(authenticate_user)):
    return {"message": f"Hello, user {user_id}"}
