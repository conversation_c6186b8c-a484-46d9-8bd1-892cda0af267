import uuid
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["chatbots"],
)


# Get chatbot session list by module ID
@router.get("/chatbot-session-list-by-module")
async def get_chatbot_session_list_by_module(module_id: str = Query(...), user_id: str = Depends(authenticate_user)):
    # Getting user information - only select the role_name field
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    # Query chatbot IDs according to the module ID - only select necessary fields
    chatbot_modules_response = supabase \
        .from_("modules_chatbots") \
        .select("chatbot_id, deleted_at") \
        .eq("module_id", module_id) \
        .is_("deleted_at", "null") \
        .execute()

    if not chatbot_modules_response.data:
        return []

    # Get chatbot IDs directly with filter in the query
    chatbot_ids = [chatbot_module['chatbot_id'] for chatbot_module in chatbot_modules_response.data]
    
    if not chatbot_ids:
        return []

    # Query chatbots with all necessary data in one go
    chatbots_response = supabase \
        .from_("chatbots_v2") \
        .select("*") \
        .in_("chatbot_id", chatbot_ids) \
        .execute()

    if not chatbots_response.data:
        return []

    # Query chat sessions for this user and these chatbots
    chat_sessions_response = supabase \
        .from_("chat_sessions_v2") \
        .select("*") \
        .in_("chatbot_id", chatbot_ids) \
        .eq("user_id", user_id) \
        .is_("deleted_at", "null") \
        .execute()

    # Group chat sessions by chatbot ID
    sessions_by_chatbot = {}
    for session in chat_sessions_response.data:
        chatbot_id = session['chatbot_id']
        if chatbot_id not in sessions_by_chatbot:
            sessions_by_chatbot[chatbot_id] = []
        sessions_by_chatbot[chatbot_id].append(session)

    # Combine chatbots with chat sessions
    result = []
    for chatbot in chatbots_response.data:
        chatbot_id = chatbot['chatbot_id']
        chatbot['sessions'] = sessions_by_chatbot.get(chatbot_id, [])
        result.append(chatbot)

    return result


@router.get("/chatbot-session-list-by-module-and-chatbot")
async def get_chatbot_session_list_by_module_and_chatbot(
    module_id: str = Query(...), 
    chatbot_id: str = Query(...), 
    user_id: str = Depends(authenticate_user)
):
    # First verify this chatbot belongs to the module
    module_chatbot_response = supabase \
        .from_("modules_chatbots") \
        .select("*") \
        .eq("module_id", module_id) \
        .eq("chatbot_id", chatbot_id) \
        .is_("deleted_at", "null") \
        .execute()
    
    if not module_chatbot_response.data:
        return []  # This chatbot doesn't belong to this module or has been deleted
        
    # Query only the specific chatbot
    chatbot_response = supabase \
        .from_("chatbots_v2") \
        .select("*") \
        .eq("chatbot_id", chatbot_id) \
        .execute()

    if not chatbot_response.data:
        return []

    # Query only sessions for this specific chatbot within this module
    chat_sessions_response = supabase \
        .from_("chat_sessions_v2") \
        .select("*") \
        .eq("chatbot_id", chatbot_id) \
        .eq("module_id", module_id) \
        .eq("user_id", user_id) \
        .is_("deleted_at", "null") \
        .execute()

    # Add sessions to the chatbot
    chatbot = chatbot_response.data[0]
    chatbot["sessions"] = chat_sessions_response.data or []

    return chatbot


@router.get("/chatbot-usage-session-list")
async def get_chatbot_usage_session_list(student_id: str = Query(...), chatbot_id: str = Query(...),
                                         module_id: str = Query(...), user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    chat_sessions_response = None
    if user_role == "Student":
        raise HTTPException(status_code=403, detail="You are not authorized to view chatbot usage sessions")
    elif user_role == "Teacher":
        # Query chatbot sessions according to the chatbot ID
        chat_sessions_response = supabase \
            .from_("chat_sessions_v2") \
            .select("*") \
            .eq("chatbot_id", chatbot_id) \
            .eq("user_id", student_id) \
            .eq("module_id", module_id) \
            .execute()

    if not chat_sessions_response.data:
        return []

    # Filter out deleted chat sessions
    chat_sessions = [chat_session for chat_session in chat_sessions_response.data if not chat_session["deleted_at"]]

    return chat_sessions


@router.get("/chatbot-usage-session-list-for-all-users")
async def get_chatbot_usage_session_list_for_all_users(chatbot_id: str = Query(...), module_id: str = Query(...),
                                                  user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    chat_sessions_response = None

    if user_role == "Student":
        raise HTTPException(status_code=403, detail="You are not authorized to view chatbot usage sessions")
    elif user_role == "Teacher":
        # Query chatbot sessions according to the chatbot ID
        chat_sessions_response = supabase \
            .from_("chat_sessions_v2") \
            .select("*") \
            .eq("chatbot_id", chatbot_id) \
            .eq("module_id", module_id) \
            .execute()

    if not chat_sessions_response.data:
        return []

    # Filter out deleted chat sessions
    chat_sessions = [chat_session for chat_session in chat_sessions_response.data if not chat_session["deleted_at"]]

    return chat_sessions


@router.get("/chatbot")
async def get_chatbot(chatbot_id: str = Query(...)):
    response = supabase.from_("chatbots_v2").select("*").eq("chatbot_id", chatbot_id).execute()

    if not response.data:
        return []

    chatbot_data = response.data[0]
    return chatbot_data


class AddChatbotData(BaseModel):
    chatbot_name: str
    model_name: str
    system_prompt: str
    welcome_prompt: str
    temperature: float
    type_name: str
    description: Dict[str, Any] = {}


@router.post("/chatbot")
async def add_chatbot(chatbot_data: AddChatbotData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    response = None
    if user_role == "Student":
        raise HTTPException(status_code=403, detail="You are not authorized to create a chatbot")
    elif user_role == "Teacher":
        # Generate a unique identifier
        unique_id = str(uuid.uuid4())[:5]

        # Insert a new chatbot into the database
        response = supabase.from_("chatbots_v2").insert([{
            "creator_user_id": user_id,
            "chatbot_name": chatbot_data.chatbot_name,
            "chatbot_unique_name": "_".join(chatbot_data.chatbot_name.lower().split()) + "_" + unique_id,
            "model_name": chatbot_data.model_name,
            "system_prompt": chatbot_data.system_prompt,
            "welcome_prompt": chatbot_data.welcome_prompt,
            "temperature": chatbot_data.temperature,
            "type_name": chatbot_data.type_name,
            "description": chatbot_data.description,
        }]).execute()

    if not response.data:
        return []

    new_chatbot_data = response.data[0]
    return new_chatbot_data


class UpdateChatbotData(BaseModel):
    chatbot_id: str
    chatbot_name: str
    model_name: str
    system_prompt: str
    welcome_prompt: str
    temperature: float
    type_name: str
    description: Dict[str, Any] = {}


@router.put("/chatbot")
async def update_chatbot(chatbot_data: UpdateChatbotData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    response = None
    if user_role == "Student":
        raise HTTPException(status_code=403, detail="You are not authorized to update a chatbot")
    elif user_role == "Teacher":
        # Generate a unique identifier
        unique_id = str(uuid.uuid4())[:5]

        # Update the chatbot information
        response = supabase.from_("chatbots_v2").update({
            "chatbot_name": chatbot_data.chatbot_name,
            "chatbot_unique_name": "_".join(chatbot_data.chatbot_name.lower().split()) + "_" + unique_id,
            "model_name": chatbot_data.model_name,
            "system_prompt": chatbot_data.system_prompt,
            "welcome_prompt": chatbot_data.welcome_prompt,
            "temperature": chatbot_data.temperature,
            "type_name": chatbot_data.type_name,
            "description": chatbot_data.description,
            "updated_at": datetime.datetime.utcnow().isoformat(),
        }) \
            .eq("chatbot_id", chatbot_data.chatbot_id) \
            .execute()

    if not response.data:
        return []

    updated_chatbot_data = response.data[0]
    return updated_chatbot_data


@router.get("/chatbot-list")
async def get_chatbot_list(user_id: str = Depends(authenticate_user)):
    # Getting information about the chatbot
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    chatbot_data = []
    # Query chatbot list according to user roles
    if user_role == "Student":
        # Student Role: Enquire about chatbots associated with a student's chosen course
        chatbot_modules_response = supabase \
            .from_("modules_chatbots") \
            .select("chatbot_id") \
            .execute()

        if not chatbot_modules_response.data:
            return []

        chatbot_ids = [chatbot_module['chatbot_id'] for chatbot_module in chatbot_modules_response.data]

        response = supabase \
            .from_("chatbots_v2") \
            .select("*") \
            .in_("chatbot_id", chatbot_ids) \
            .execute()

        if not response.data:
            return []

        # Filter the deleted_at field
        chatbot_data = [chatbot for chatbot in response.data if not chatbot["deleted_at"]]
    elif user_role == "Teacher":
        # Teacher Role: Enquire about chatbots associated with courses taught by teachers
        response = supabase \
            .from_("chatbots_v2") \
            .select("*") \
            .eq("creator_user_id", user_id) \
            .execute()

        if not response.data:
            return []

        # Filter the deleted_at field
        chatbot_data = [chatbot for chatbot in response.data if not chatbot["deleted_at"]]

    return chatbot_data


# Get chatbot list by module ID (Return chatbots that not associated with the current module)
@router.get("/chatbot-list-by-module")
async def get_chatbot_list_by_module(module_id: str = Query(...), user_id: str = Depends(authenticate_user)):
    # Getting information about the chatbot
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    if user_role != "Teacher":
        raise HTTPException(status_code=403, detail="You are not authorized to view chatbots by module")

    # Query chatbot IDs according to the module ID
    chatbot_modules_response = supabase \
        .from_("modules_chatbots") \
        .select("*") \
        .eq("module_id", module_id) \
        .execute()

    # Get chatbot IDs, filter out deleted chatbots
    chatbot_ids = [chatbot_module['chatbot_id'] for chatbot_module in chatbot_modules_response.data if chatbot_module['deleted_at'] is None]

    # Query chatbots that are not associated with the current module
    response = supabase.from_("chatbots_v2") \
        .select("*") \
        .eq("creator_user_id", user_id) \
        .execute()

    if not response.data:
        return []

    all_data = response.data

    # Filter out records where chatbot_id is in chatbot_ids, filter out deleted chatbots
    filtered_data = [data for data in all_data if data['chatbot_id'] not in chatbot_ids and data['deleted_at'] is None]

    if not filtered_data:
        return []

    return filtered_data


@router.delete("/chatbot")
async def delete_chatbot(chatbot_id: str, user_id: str = Depends(authenticate_user)):
    # Getting information about the chatbot
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Query chatbot information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=403, detail="You are not authorized to delete a chatbot")
    elif user_role == "Teacher":
        # Update the chatbot's deleted_at field to mark it as deleted
        response = supabase.from_("chatbots_v2") \
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()}) \
            .eq("chatbot_id", chatbot_id) \
            .execute()

    if not response.data:
        return []

    return response.data[0]


# Get chatbots associated with a module (without sessions)
@router.get("/modules-chatbots")
async def get_modules_chatbots(module_id: str = Query(...), user_id: str = Depends(authenticate_user)):
    # Query chatbot IDs according to the module ID
    chatbot_modules_response = supabase \
        .from_("modules_chatbots") \
        .select("chatbot_id") \
        .eq("module_id", module_id) \
        .is_("deleted_at", "null") \
        .execute()

    if not chatbot_modules_response.data:
        return []

    # Get chatbot IDs
    chatbot_ids = [chatbot_module['chatbot_id'] for chatbot_module in chatbot_modules_response.data]
    
    if not chatbot_ids:
        return []

    # Query chatbots basic information without sessions
    chatbots_response = supabase \
        .from_("chatbots_v2") \
        .select("*") \
        .in_("chatbot_id", chatbot_ids) \
        .is_("deleted_at", "null") \
        .execute()

    if not chatbots_response.data:
        return []

    return chatbots_response.data
