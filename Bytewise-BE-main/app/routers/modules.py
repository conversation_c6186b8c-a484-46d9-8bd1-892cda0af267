from typing import List

from fastapi import APIRouter, Depends, HTTPException, Query
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["modules"],
)


# Get module list by course ID
@router.get("/module-list")
async def get_module_list(course_id: str = Query(...)):
    # Query module information according to the provided course ID
    response = supabase.from_("modules").select("*").eq("course_id", course_id).execute()

    if not response.data:
        return []

    module_data = response.data
    return module_data


@router.get("/module-chatbot-list")
async def get_module_chatbot_list(course_id: str = Query(...)):
    # Query module-chatbot information from the created view
    response = supabase \
        .from_("module_chatbot_item") \
        .select("*") \
        .eq("course_id", course_id) \
        .execute()
    modules_chatbots = response.data

    # Group by module_id
    modules_dict = {}
    for item in modules_chatbots:
        module_id = item['module_id']
        # Remember to handle with module_deleted_at field
        if module_id not in modules_dict and not item['module_deleted_at']:
            modules_dict[module_id] = {
                'module_id': module_id,
                'course_id': item['course_id'],
                'module_title': item['module_title'],
                'description': item['description'],
                'created_at': item['module_created_at'],
                'chatbots': []
            }
        # Remember to handle with all deleted_ats field
        if not item['module_deleted_at'] and not item['chatbot_deleted_at'] and not item['module_chatbot_deleted_at']:
            modules_dict[module_id]['chatbots'].append({
                'chatbot_id': item['chatbot_id'],
                'chatbot_name': item['chatbot_name'],
                'created_at': item['chatbot_created_at'],
                'creator_user_id': item['creator_user_id'],
                'creator_user_full_name': item['creator_user_full_name'],
            })

    # Convert dictionary to list
    modules_with_chatbots = list(modules_dict.values())

    return modules_with_chatbots


class ModuleChatbotData(BaseModel):
    module_id: str
    chatbot_ids: List[str]


@router.post("/module-chatbots")
async def add_module_chatbots(module_chatbot: ModuleChatbotData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Import chatbots to the module according to user roles
    response = None
    if user_role == "Student":
        return []
    elif user_role == "Teacher":
        # Insert chatbots to the module
        response = supabase.from_("modules_chatbots")\
            .insert([{
                "module_id": module_chatbot.module_id,
                "chatbot_id": chatbot_id
            } for chatbot_id in module_chatbot.chatbot_ids])\
            .execute()

    if not response.data:
        return []

    return response.data


@router.delete("/module-chatbot")
async def delete_module_chatbot(module_id: str, chatbot_id: str, user_id: str = Depends(authenticate_user)):
    # Getting information about the course
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Query course information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=404, detail="Student cannot delete module chatbot")
    elif user_role == "Teacher":
        # Update the module's deleted_at field to mark it as deleted
        response = supabase.from_("modules_chatbots")\
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()})\
            .eq("module_id", module_id)\
            .eq("chatbot_id", chatbot_id)\
            .execute()

    if not response.data:
        return []

    return response.data[0]


@router.delete("/module")
async def delete_module(module_id: str, user_id: str = Depends(authenticate_user)):
    # Getting information about the course
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Query course information according to user roles
    response = None
    if user_role == "Student":
        return []
    elif user_role == "Teacher":
        # Update the module's deleted_at field to mark it as deleted
        response = supabase.from_("modules")\
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()})\
            .eq("module_id", module_id)\
            .execute()

    if not response.data:
        return []

    return response.data[0]


class CreateModuleData(BaseModel):
    course_id: str
    module_title: str


@router.post("/module")
async def add_module(module_data: CreateModuleData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Query course information according to user roles
    response = None
    if user_role == "Student":
        return []
    elif user_role == "Teacher":
        # Insert a new module
        response = supabase.from_("modules")\
            .insert([{
                "course_id": module_data.course_id,
                "module_title": module_data.module_title
            }])\
            .execute()

    if not response.data:
        return []

    return response.data[0]


class RenameModuleData(BaseModel):
    module_id: str
    module_title: str


@router.put("/module")
async def rename_module(module_data: RenameModuleData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Update module information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=404, detail="Student cannot rename module")
    elif user_role == "Teacher":
        response = supabase.from_("modules")\
            .update({"module_title": module_data.module_title})\
            .eq("module_id", module_data.module_id)\
            .execute()

    if not response.data:
        return []

    return response.data[0]
