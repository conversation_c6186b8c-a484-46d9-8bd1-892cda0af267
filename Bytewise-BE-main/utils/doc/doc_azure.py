import time

from azure.core.credentials import AzureKeyCredential
from azure.ai.formrecognizer import DocumentAnalysisClient
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Define the Azure Document Intelligence endpoint and key
azure_document_intelligence_api_key = os.environ.get("AZURE_DOCUMENT_INTELLIGENCE_API_KEY")
azure_document_intelligence_endpoint = os.environ.get("AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT")

# Create a Document Analysis client
document_analysis_client = DocumentAnalysisClient(
    endpoint=azure_document_intelligence_endpoint,
    credential=AzureKeyCredential(azure_document_intelligence_api_key)
)


# Define the function to analyze a document
def analyze_document(file_path: str):
    print("Use Azure Document Intelligence")
    start_time = time.time()
    # Analyze the document
    with open(file_path, "rb") as document:
        poller = document_analysis_client.begin_analyze_document(
            model_id="prebuilt-read",
            document=document,
        )
    res = poller.result()

    file_content = res.content

    end_time = time.time()
    time_elapsed = end_time - start_time
    print("Time Elapsed:", time_elapsed, "seconds")

    return file_content, res, time_elapsed
