import os
import time
from openai import AzureOpenAI
from dotenv import load_dotenv
import json

load_dotenv()
azure_openai_api_hkbulc2_key = os.environ.get("AZURE_OPENAI_API_HKBULC2_KEY")
azure_openai_api_hkbulc2_endpoint = os.environ.get("AZURE_OPENAI_API_HKBULC2_ENDPOINT")


def parse_azure_dalle_api_res(session_id, chatbot_id, res, time_elapsed):
    # Prepare the record for database insertion
    try:
        # Extracting the first item in the 'data' array as it contains the relevant details
        first_item = res['data'][0]
        content_filter_results = first_item['content_filter_results']
        prompt_filter_results = first_item['prompt_filter_results']

        # Constructing the record to be inserted into the database
        db_record = {
            "session_id": session_id,
            "chatbot_id": chatbot_id,
            "api_provider": "Azure DALL·E",
            "api_created": res.get("created", 0),
            "api_model": "dall-e-3",
            "generated_image_url": first_item.get("url", ""),
            "revised_prompt": first_item.get("revised_prompt", ""),
            "content_filter_hate_filtered": content_filter_results['hate']['filtered'],
            "content_filter_hate_severity": content_filter_results['hate']['severity'],
            "content_filter_self_harm_filtered": content_filter_results['self_harm']['filtered'],
            "content_filter_self_harm_severity": content_filter_results['self_harm']['severity'],
            "content_filter_sexual_filtered": content_filter_results['sexual']['filtered'],
            "content_filter_sexual_severity": content_filter_results['sexual']['severity'],
            "content_filter_violence_filtered": content_filter_results['violence']['filtered'],
            "content_filter_violence_severity": content_filter_results['violence']['severity'],
            "prompt_filter_hate_filtered": prompt_filter_results['hate']['filtered'],
            "prompt_filter_hate_severity": prompt_filter_results['hate']['severity'],
            "prompt_filter_profanity_detected": prompt_filter_results['profanity']['detected'],
            "prompt_filter_profanity_filtered": prompt_filter_results['profanity']['filtered'],
            "prompt_filter_self_harm_filtered": prompt_filter_results['self_harm']['filtered'],
            "prompt_filter_self_harm_severity": prompt_filter_results['self_harm']['severity'],
            "prompt_filter_sexual_filtered": prompt_filter_results['sexual']['filtered'],
            "prompt_filter_sexual_severity": prompt_filter_results['sexual']['severity'],
            "prompt_filter_violence_filtered": prompt_filter_results['violence']['filtered'],
            "prompt_filter_violence_severity": prompt_filter_results['violence']['severity'],
            "tti_time_elapsed": time_elapsed
        }
    except AttributeError as e:
        print(f"AttributeError encountered: {e}. 'res' type: {type(res)}")
        # If the response is not in the expected dictionary format, return None.
        db_record = None
    except Exception as e:
        print(f"Exception encountered: {e}. 'res' type: {type(res)}")
        # If an unexpected exception occurs, return None.
        db_record = None

    return db_record


def image_by_azure_dalle(conversation_list, model_name="dall-e-3"):
    print("Use Azure Dall-E API")

    start_time = time.time()

    # Generate response by accessing Azure Dall-E API
    dalle = AzureOpenAI(
        azure_endpoint=azure_openai_api_hkbulc2_endpoint,
        api_key=azure_openai_api_hkbulc2_key,
        api_version="2024-02-01",
    )

    response = dalle.images.generate(
        model=model_name,
        prompt=conversation_list[0]["content"] + ", " + conversation_list[-1]["content"],
        n=1,
        size="1024x1024",
    )

    res = json.loads(response.model_dump_json())
    print(res)
    image_url = res['data'][0]['url']
    chat_response = f"![Image]({image_url})"

    end_time = time.time()
    time_elapsed = end_time - start_time
    print("Time elapsed:", time_elapsed, "seconds")

    return chat_response, res, time_elapsed
