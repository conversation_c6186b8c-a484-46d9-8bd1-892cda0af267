from fastapi import APIRouter, Depends, HTTPException, Query
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
from dotenv import load_dotenv
from typing import List
import datetime

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["avatars"],
)


@router.delete("/delete_avatar")
async def delete_chatbot(avatar_id: str, user_id: str = Depends(authenticate_user)):
    # Getting information about the chatbot
    user_response = (
        supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    )
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Query chatbot information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(
            status_code=403, detail="You are not authorized to delete a chatbot"
        )
    elif user_role == "Teacher":
        # Update the chatbot's deleted_at field to mark it as deleted
        response = (
            supabase.from_("avatars")
            .update(
                {"deleted_at": datetime.datetime.now(datetime.timezone.utc).isoformat()}
            )
            .eq("id", avatar_id)
            .execute()
        )

    if not response.data:
        return []

    return response.data[0]


@router.get("/get_avatar_knowledge/{avatar_id}")
async def get_avatar(avatar_id: str, user_id: str = Depends(authenticate_user)):
    try:
        # Check if the user exists
        user_response = (
            supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        )

        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")

        # Retrieve the avatar
        avatar_response = (
            supabase.from_("avatars")
            .select("knowledge_base")
            .eq("id", avatar_id)
            .eq(
                "creator_user_id", user_id
            )  # Ensures user can only access their own avatars
            .execute()
        )

        if not avatar_response.data:
            raise HTTPException(
                status_code=404, detail="Avatar not found or access denied"
            )

        # Return avatar data
        return {"avatar": avatar_response.data[0]}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")


@router.delete("/delete-module-avatar")
async def delete_module_chatbot(
    module_id: str, avatar_id: str, user_id: str = Depends(authenticate_user)
):
    # Getting information about the course
    user_response = (
        supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    )
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Query course information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(
            status_code=404, detail="Student cannot delete module chatbot"
        )
    elif user_role == "Teacher":
        # Update the module's deleted_at field to mark it as deleted
        response = (
            supabase.from_("modules_avatars")
            .update(
                {"deleted_at": datetime.datetime.now(datetime.timezone.utc).isoformat()}
            )
            .eq("module_id", module_id)
            .eq("avatar_id", avatar_id)
            .execute()
        )

    if not response.data:
        return []

    return response.data[0]


@router.get("/module-avatar-list")
async def get_module_chatbot_list(course_id: str = Query(...)):
    response = (
        supabase.from_("module_avatar_item")
        .select("*")
        .eq("course_id", course_id)
        .execute()
    )
    modules_avatars = response.data

    # Group by module_id
    modules_dict = {}
    for item in modules_avatars:
        module_id = item["module_id"]
        # Remember to handle with module_deleted_at field
        if module_id not in modules_dict and not item["module_deleted_at"]:
            modules_dict[module_id] = {
                "module_id": module_id,
                "course_id": item["course_id"],
                "avatars": [],
            }
        # Remember to handle with all deleted_ats field
        if (
            not item["module_deleted_at"]
            and not item["avatar_deleted_at"]
            and not item["module_avatar_deleted_at"]
        ):
            modules_dict[module_id]["avatars"].append(
                {
                    "avatar_id": item["avatar_id"],
                    "avatar_name": item["avatar_name"],
                    "created_at": item["avatar_created_at"],
                    "creator_user_id": item["creator_user_id"],
                    "creator_user_full_name": item["creator_user_full_name"],
                }
            )

    # Convert dictionary to list
    modules_with_chatbots = list(modules_dict.values())

    return modules_with_chatbots


class ModuleAvatar(BaseModel):
    module_id: str
    avatar_ids: List[str]


@router.post("/module-avatars")
async def add_module_chatbots(
    module_avatar: ModuleAvatar, user_id: str = Depends(authenticate_user)
):
    # Getting user information
    user_response = (
        supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    )
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")
    user_role = user_response.data[0]["role_name"]

    # Import chatbots to the module according to user roles
    response = None
    if user_role == "Student":
        return []
    elif user_role == "Teacher":
        # Insert chatbots to the module
        response = (
            supabase.from_("modules_avatars")
            .insert(
                [
                    {"module_id": module_avatar.module_id, "avatar_id": avatar_ids}
                    for avatar_ids in module_avatar.avatar_ids
                ]
            )
            .execute()
        )

    if not response.data:
        return []

    return response.data


@router.get("/avatar-list-by-module")
async def get_avatar_list_by_module(
    module_id: str = Query(...), user_id: str = Depends(authenticate_user)
):
    try:
        # 1. 获取用户角色
        user_response = (
            supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        )

        if not user_response.data:
            raise HTTPException(
                status_code=404, detail=f"User not found (user_id: {user_id})"
            )

        user_role = user_response.data[0]["role_name"]

        if user_role != "Teacher":
            raise HTTPException(
                status_code=403,
                detail=f"Permission denied. Required role: Teacher, your role: {user_role}",
            )

        # 2. 获取模块关联的avatar
        avatar_modules_response = (
            supabase.from_("modules_avatars")
            .select("*")
            .eq("module_id", module_id)
            .execute()
        )

        # 3. 提取avatar_id列表
        avatar_ids = [
            item["avatar_id"]
            for item in avatar_modules_response.data
            if item["deleted_at"] is None
        ]
        # Query chatbots that are not associated with the current module
        response = (
            supabase.from_("avatars")
            .select("*")
            .eq("creator_user_id", user_id)
            .execute()
        )
        if not response.data:
            return []

        all_data = response.data

        filtered_data = [
            data
            for data in all_data
            if data["id"] not in avatar_ids and data["deleted_at"] is None
        ]

        if not filtered_data:
            return []

        return filtered_data

    except HTTPException:
        raise
    except Exception as e:
        # 返回详细的错误信息
        raise HTTPException(
            status_code=500, detail=f"Internal error occurred: {str(e)}"
        )


@router.post("/create_new_avatar")
async def add_chatbot(avatar_data: dict, user_id: str = Depends(authenticate_user)):
    try:
        # 检查用户角色
        user_response = (
            supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        )
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")

        user_role = user_response.data[0]["role_name"]

        if user_role != "Teacher":
            raise HTTPException(
                status_code=403, detail="Only Teachers are authorized to create avatars"
            )

        # 从avatar_data中提取name和knowledge
        name = avatar_data.get("name")
        knowledge = avatar_data.get("knowledge")

        if not name or not knowledge:
            raise HTTPException(
                status_code=400, detail="Name and knowledge are required fields"
            )

        # 准备要插入的数据
        data_to_insert = {
            "avatar_name": name,
            "knowledge_base": knowledge,
            "creator_user_id": user_id,
        }

        # 插入数据到Supabase
        response = supabase.table("avatars").insert(data_to_insert).execute()

        # 检查是否有错误
        if hasattr(response, "error") and response.error:
            raise HTTPException(status_code=500, detail=str(response.error))

        return {
            "message": "Avatar created successfully",
            "data": data_to_insert,
            "supabase_response": response.data,
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")


@router.get("/get_avatar_list")
async def get_avatar_list(user_id: str = Depends(authenticate_user)):
    try:
        response = (
            supabase.table("avatars")
            .select("*")
            .eq("creator_user_id", user_id)
            .is_("deleted_at", "null")
            .execute()
        )
        return {"success": True, "data": response.data}

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={"success": False, "error": f"获取avatar列表时出错: {str(e)}"},
        )


@router.put("/update_avatar/{avatar_id}")
async def update_avatar(
    avatar_id: str, avatar_data: dict, user_id: str = Depends(authenticate_user)
):
    try:
        # Check user role
        user_response = (
            supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        )
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")

        user_role = user_response.data[0]["role_name"]

        if user_role != "Teacher":
            raise HTTPException(
                status_code=403, detail="Only Teachers are authorized to update avatars"
            )

        # Check if avatar exists and belongs to the user
        avatar_response = (
            supabase.from_("avatars")
            .select("*")
            .eq("id", avatar_id)
            .eq("creator_user_id", user_id)
            .execute()
        )

        if not avatar_response.data:
            raise HTTPException(
                status_code=404,
                detail="Avatar not found or you don't have permission to edit it",
            )

        # Extract name and knowledge from avatar_data
        name = avatar_data.get("name")
        knowledge = avatar_data.get("knowledge")

        if not name or not knowledge:
            raise HTTPException(
                status_code=400, detail="Name and knowledge are required fields"
            )

        # Prepare data for update
        update_data = {
            "avatar_name": name,
            "knowledge_base": knowledge,
        }

        # Update the avatar in Supabase
        response = (
            supabase.table("avatars").update(update_data).eq("id", avatar_id).execute()
        )

        # Check for errors
        if hasattr(response, "error") and response.error:
            raise HTTPException(status_code=500, detail=str(response.error))

        return {
            "message": "Avatar updated successfully",
            "data": update_data,
            "supabase_response": response.data,
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")
