from fastapi import APIRouter, Depends, HTTPException, Query
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
import datetime
from dotenv import load_dotenv
from typing import List, Optional

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["internal_messages"],
)


# Data Models
class InternalMessage(BaseModel):
    message_id: str
    session_id: str
    sender_user_id: str
    receiver_user_id: str
    message_content: str
    message_type: str
    is_read: bool
    is_sent_to_chatbot: bool
    sender_role: Optional[str] = None
    receiver_role: Optional[str] = None
    created_at: Optional[str] = None


# Simplified Data Models to match frontend expectations
class SendInternalMessageData(BaseModel):
    session_id: str
    content: str  # Match frontend field name
    recipient_role: str  # Let backend handle user ID resolution


class MarkReadData(BaseModel):
    session_id: str
    user_role: str  # Simplified for frontend


class MarkSentToChatbotData(BaseModel):
    message_id: str


# Keep original structure for backward compatibility
class CreateRaiseHandNotificationData(BaseModel):
    course_id: str
    creator_user_id: str  # teacher user_id
    notification_title: str
    description: dict


# New simplified model for frontend-friendly interface
class CreateRaiseHandNotificationSimpleData(BaseModel):
    session_id: str
    session_name: str
    course_id: str
    course_title: str
    module_id: str
    module_title: str
    chatbot_id: str


# New model for teacher reply notifications
class CreateTeacherReplyNotificationData(BaseModel):
    session_id: str
    session_name: str
    course_id: str
    course_title: str
    module_id: str
    module_title: str
    chatbot_id: str
    teacher_user_id: str
    teacher_name: str
    reply_content: str  # Preview of reply content (first 100 chars)
    student_user_id: str


# Helper function to resolve recipient user_id
async def resolve_recipient_user_id(session_id: str, sender_role: str, course_id: str):
    """Resolve recipient user ID based on sender role"""
    try:
        if sender_role == "Student":
            # Find teacher for this course using correct table structure
            # Query groups_courses table to find teacher_id for the given course_id
            teachers_response = supabase.from_("groups_courses") \
                .select("teacher_id") \
                .eq("course_id", course_id) \
                .limit(1) \
                .execute()
            
            if not teachers_response.data:
                raise HTTPException(status_code=404, detail=f"No teacher found for course {course_id}")
            
            return teachers_response.data[0]["teacher_id"]
        else:
            # Teacher sending to student - find session owner
            session_response = supabase.from_("chat_sessions_v2") \
                .select("user_id") \
                .eq("session_id", session_id) \
                .execute()
            
            if not session_response.data:
                raise HTTPException(status_code=404, detail=f"Session {session_id} not found")
            
            return session_response.data[0]["user_id"]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error resolving recipient: {str(e)}")


# Helper function to create teacher reply notification
async def create_teacher_reply_notification(
    session_id: str, 
    teacher_user_id: str, 
    student_user_id: str, 
    reply_content: str
):
    """Create teacher reply notification for student"""
    try:
        # Get session details
        session_response = supabase.from_("chat_sessions_v2") \
            .select("session_name, module_id, chatbot_id") \
            .eq("session_id", session_id) \
            .execute()
        
        if not session_response.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session_data = session_response.data[0]
        
        # Get module and course details
        module_response = supabase.from_("modules") \
            .select("module_title, course_id") \
            .eq("module_id", session_data["module_id"]) \
            .execute()
        
        if not module_response.data:
            raise HTTPException(status_code=404, detail="Module not found")
        
        module_data = module_response.data[0]
        
        # Get course details
        course_response = supabase.from_("courses") \
            .select("course_title") \
            .eq("course_id", module_data["course_id"]) \
            .execute()
        
        if not course_response.data:
            raise HTTPException(status_code=404, detail="Course not found")
        
        course_data = course_response.data[0]
        
        # Get teacher details
        teacher_response = supabase.from_("users") \
            .select("full_name") \
            .eq("user_id", teacher_user_id) \
            .execute()
        
        if not teacher_response.data:
            raise HTTPException(status_code=404, detail="Teacher not found")
        
        teacher_name = teacher_response.data[0]["full_name"]
        
        # Create preview of reply content (first 100 characters)
        reply_preview = reply_content[:100] + "..." if len(reply_content) > 100 else reply_content
        
        # Insert teacher reply notification into notifications table (similar to raise_hand notifications)
        # This will be automatically visible to students through the notification_item view
        notification_response = supabase.from_("notifications").insert([{
            "course_id": module_data["course_id"],
            "creator_user_id": student_user_id,  # Use student as creator for notification_item view
            "notification_title": f"Teacher {teacher_name} replied to your question",
            "description": {
                "type": "teacher_reply",
                "session_id": session_id,
                "session_name": session_data["session_name"],
                "module_id": session_data["module_id"],
                "module_title": module_data["module_title"],
                "chatbot_id": session_data["chatbot_id"],
                "course_id": module_data["course_id"],
                "course_title": course_data["course_title"],
                "teacher_user_id": teacher_user_id,
                "teacher_name": teacher_name,
                "reply_content": reply_preview
            }
        }]).execute()
        
        if not notification_response.data:
            raise HTTPException(status_code=500, detail="Failed to create teacher reply notification")
        
        return notification_response.data[0]["notification_id"]
        
    except Exception as e:
        print(f"Error creating teacher reply notification: {str(e)}")
        # Don't fail the main operation if notification creation fails
        return None


@router.get("/internal-messages")
async def get_internal_messages(
    session_id: str = Query(...), 
    user_id: str = Depends(authenticate_user)
):
    """
    Fetch all internal messages for a specific session with user details.
    Returns messages with sender_role and receiver_role derived from users table.
    """
    try:
        # First verify that the user has access to this session
        session_response = supabase.from_("chat_sessions_v2").select("user_id").eq("session_id", session_id).execute()
        if not session_response.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session_owner = session_response.data[0]["user_id"]
        
        # Get user role to determine access permissions
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        # Students can only access their own sessions, teachers can access any session
        if user_role == "Student" and session_owner != user_id:
            raise HTTPException(status_code=403, detail="Access denied to this session")
        
        # Query internal messages first (simplified query without JOIN)
        messages_response = supabase.from_("internal_messages") \
            .select("message_id, session_id, sender_user_id, receiver_user_id, message_content, message_type, is_read, is_sent_to_chatbot, created_at") \
            .eq("session_id", session_id) \
            .order("created_at") \
            .execute()
        
        if not messages_response.data:
            return []
        
        # Get unique user IDs from messages
        user_ids = set()
        for message in messages_response.data:
            user_ids.add(message["sender_user_id"])
            user_ids.add(message["receiver_user_id"])
        
        # Fetch user roles for all involved users
        users_response = supabase.from_("users") \
            .select("user_id, role_name") \
            .in_("user_id", list(user_ids)) \
            .execute()
        
        # Create a mapping of user_id to role_name
        user_roles = {}
        if users_response.data:
            for user in users_response.data:
                user_roles[user["user_id"]] = user["role_name"]
        
        # Transform the response to include sender_role and receiver_role
        formatted_messages = []
        for message in messages_response.data:
            formatted_message = {
                "message_id": message["message_id"],
                "session_id": message["session_id"],
                "sender_user_id": message["sender_user_id"],
                "receiver_user_id": message["receiver_user_id"],
                "message_content": message["message_content"],
                "message_type": message["message_type"],
                "is_read": message["is_read"],
                "is_sent_to_chatbot": message["is_sent_to_chatbot"],
                "created_at": message["created_at"],
                "sender_role": user_roles.get(message["sender_user_id"]),
                "receiver_role": user_roles.get(message["receiver_user_id"])
            }
            formatted_messages.append(formatted_message)
        
        return formatted_messages
        
    except HTTPException:
        # Re-raise HTTP exceptions as they are
        raise
    except Exception as e:
        print(f"Unexpected error in get_internal_messages: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch messages: {str(e)}")


@router.post("/internal-messages")
async def send_internal_message(
    data: SendInternalMessageData, 
    user_id: str = Depends(authenticate_user)
):
    """
    Send internal message with simplified frontend interface.
    Backend resolves recipient user_id automatically.
    """
    try:
        # Debug: Log the incoming data
        print(f"Received data: {data}")
        print(f"User ID: {user_id}")
        
        # Get sender info
        sender_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not sender_response.data:
            raise HTTPException(status_code=404, detail="Sender not found")
        
        sender_role = sender_response.data[0]["role_name"]
        print(f"Sender role: {sender_role}")
        
        # Get session info first
        session_response = supabase.from_("chat_sessions_v2") \
            .select("user_id, module_id") \
            .eq("session_id", data.session_id) \
            .execute()
        
        if not session_response.data:
            raise HTTPException(status_code=404, detail=f"Session {data.session_id} not found")
        
        session_data = session_response.data[0]
        print(f"Session data: {session_data}")
        
        # Get course_id from module
        if not session_data.get("module_id"):
            raise HTTPException(status_code=404, detail="Module ID not found in session")
            
        module_response = supabase.from_("modules") \
            .select("course_id") \
            .eq("module_id", session_data["module_id"]) \
            .execute()
        
        if not module_response.data:
            raise HTTPException(status_code=404, detail=f"Module {session_data['module_id']} not found")
        
        course_id = module_response.data[0]["course_id"]
        print(f"Course ID: {course_id}")
        
        # Resolve recipient automatically based on sender role
        try:
            recipient_user_id = await resolve_recipient_user_id(data.session_id, sender_role, course_id)
            print(f"Recipient user ID: {recipient_user_id}")
        except Exception as e:
            print(f"Error resolving recipient: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to resolve recipient: {str(e)}")
        
        # Validate that sender and recipient are different
        if user_id == recipient_user_id:
            raise HTTPException(status_code=400, detail="Cannot send message to yourself")
        
        # Insert the message
        message_data = {
            "session_id": data.session_id,
            "sender_user_id": user_id,
            "receiver_user_id": recipient_user_id,
            "message_content": data.content,  # Match frontend field
            "message_type": "message",
            "is_read": False,
            "is_sent_to_chatbot": False
        }
        print(f"Inserting message: {message_data}")
        
        message_response = supabase.from_("internal_messages").insert([message_data]).execute()
        
        if not message_response.data:
            raise HTTPException(status_code=500, detail="Failed to insert message into database")
        
        # If teacher is replying to student, create teacher reply notification
        if sender_role == "Teacher":
            try:
                notification_id = await create_teacher_reply_notification(
                    session_id=data.session_id,
                    teacher_user_id=user_id,
                    student_user_id=recipient_user_id,
                    reply_content=data.content
                )
                print(f"Created teacher reply notification: {notification_id}")
            except Exception as e:
                print(f"Failed to create teacher reply notification: {str(e)}")
                # Don't fail the main operation if notification creation fails
        
        return {
            "message_id": message_response.data[0]["message_id"],
            "created_at": message_response.data[0]["created_at"]
        }
        
    except HTTPException:
        # Re-raise HTTP exceptions as they are
        raise
    except Exception as e:
        print(f"Unexpected error in send_internal_message: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.put("/internal-messages/mark-read")
async def mark_messages_as_read(
    data: MarkReadData, 
    user_id: str = Depends(authenticate_user)
):
    """
    Mark internal messages as read for a specific user in a session.
    Updates is_read = true for messages where receiver_user_id = user_id.
    """
    try:
        # Verify that the user has access to this session
        session_response = supabase.from_("chat_sessions_v2").select("user_id").eq("session_id", data.session_id).execute()
        if not session_response.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session_owner = session_response.data[0]["user_id"]
        
        # Get user role to determine permissions
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        # Students can only mark messages as read in their own sessions
        if user_role == "Student" and session_owner != user_id:
            raise HTTPException(status_code=403, detail="Access denied to this session")
        
        # Update messages as read for the current user
        update_response = supabase.from_("internal_messages") \
            .update({"is_read": True}) \
            .eq("session_id", data.session_id) \
            .eq("receiver_user_id", user_id) \
            .eq("is_read", False) \
            .execute()
        
        updated_count = len(update_response.data) if update_response.data else 0
        
        return {
            "success": True,
            "updated_count": updated_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to mark messages as read: {str(e)}")


@router.put("/internal-messages/mark-sent-to-chatbot")
async def mark_message_sent_to_chatbot(
    data: MarkSentToChatbotData, 
    user_id: str = Depends(authenticate_user)
):
    """
    Mark an internal message as sent to chatbot.
    Updates is_sent_to_chatbot = true.
    """
    try:
        # Get user role - only teachers can mark messages as sent to chatbot
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        if user_role != "Teacher":
            raise HTTPException(status_code=403, detail="Only teachers can send messages to chatbot")
        
        # Verify the message exists and get its session
        message_response = supabase.from_("internal_messages") \
            .select("session_id, is_sent_to_chatbot") \
            .eq("message_id", data.message_id) \
            .execute()
        
        if not message_response.data:
            raise HTTPException(status_code=404, detail="Message not found")
        
        if message_response.data[0]["is_sent_to_chatbot"]:
            raise HTTPException(status_code=400, detail="Message already sent to chatbot")
        
        # Update the message as sent to chatbot
        update_response = supabase.from_("internal_messages") \
            .update({"is_sent_to_chatbot": True}) \
            .eq("message_id", data.message_id) \
            .execute()
        
        if not update_response.data:
            raise HTTPException(status_code=500, detail="Failed to update message")
        
        return {"success": True}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to mark message as sent to chatbot: {str(e)}")


@router.post("/internal-message-notification")  # Match frontend path
async def create_raise_hand_notification(
    data: CreateRaiseHandNotificationSimpleData, 
    user_id: str = Depends(authenticate_user)
):
    """
    Create raise hand notification with frontend-friendly interface.
    """
    try:
        # Get student info
        student_response = supabase.from_("users").select("full_name, role_name").eq("user_id", user_id).execute()
        if not student_response.data or student_response.data[0]["role_name"] != "Student":
            raise HTTPException(status_code=403, detail="Only students can raise hand")
        
        student_name = student_response.data[0]["full_name"]
        
        # Find teacher for this course using correct table structure
        teachers_response = supabase.from_("groups_courses") \
            .select("teacher_id") \
            .eq("course_id", data.course_id) \
            .limit(1) \
            .execute()
        
        if not teachers_response.data:
            raise HTTPException(status_code=404, detail="No teacher found for this course")
        
        teacher_user_id = teachers_response.data[0]["teacher_id"]
        
        # Get the first (most recent) internal message content for this session from this student
        messages_response = supabase.from_("internal_messages") \
            .select("message_content") \
            .eq("session_id", data.session_id) \
            .eq("sender_user_id", user_id) \
            .order("created_at", desc=True) \
            .limit(1) \
            .execute()
        
        # Get message preview (first message content or default text)
        message_content = "Student is asking for help"
        if messages_response.data:
            full_message = messages_response.data[0]["message_content"]
            # Create preview of message content (first 100 characters)
            message_content = full_message[:100] + "..." if len(full_message) > 100 else full_message
        
        # Create notification
        notification_response = supabase.from_("notifications").insert([{
            "course_id": data.course_id,
            "creator_user_id": teacher_user_id,
            "notification_title": f"Student {student_name} needs help",
            "description": {
                "type": "raise_hand",
                "session_id": data.session_id,
                "session_name": data.session_name,
                "module_id": data.module_id,
                "module_title": data.module_title,
                "chatbot_id": data.chatbot_id,
                "student_user_id": user_id,
                "student_name": student_name,
                "course_title": data.course_title,
                "message_content": message_content
            }
        }]).execute()
        
        if not notification_response.data:
            raise HTTPException(status_code=500, detail="Failed to create notification")
        
        return {"notification_id": notification_response.data[0]["notification_id"]}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create notification: {str(e)}")


@router.post("/internal-messages/raise-hand-notification")
async def create_raise_hand_notification_legacy(
    data: CreateRaiseHandNotificationData, 
    user_id: str = Depends(authenticate_user)
):
    """
    Create a notification for teacher when student raises hand.
    This is typically called when a student sends their first message in a session.
    Legacy endpoint for backward compatibility.
    """
    try:
        # Verify that the user is a student (the one raising hand)
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        if user_role != "Student":
            raise HTTPException(status_code=403, detail="Only students can raise hand")
        
        # Insert the raise hand notification
        notification_response = supabase.from_("notifications").insert([{
            "course_id": data.course_id,
            "creator_user_id": data.creator_user_id,  # teacher user_id
            "notification_title": data.notification_title,
            "description": data.description
        }]).execute()
        
        if not notification_response.data:
            raise HTTPException(status_code=500, detail="Failed to create notification")
        
        return {"notification_id": notification_response.data[0]["notification_id"]}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create raise hand notification: {str(e)}")


# Additional helper endpoints for frontend
@router.get("/chat-session-info")
async def get_chat_session_info(
    session_id: str = Query(...),
    user_id: str = Depends(authenticate_user)
):
    """
    Get basic session information.
    """
    try:
        session_response = supabase.from_("chat_sessions_v2") \
            .select("user_id, session_name") \
            .eq("session_id", session_id) \
            .execute()
        
        if not session_response.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return session_response.data[0]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch session info: {str(e)}")


@router.get("/course-teachers")
async def get_course_teachers(
    course_id: str = Query(...),
    user_id: str = Depends(authenticate_user)
):
    """
    Get teachers for a specific course.
    """
    try:
        teachers_response = supabase.from_("groups_courses") \
            .select("teacher_id, users!groups_courses_teacher_id_fkey(full_name)") \
            .eq("course_id", course_id) \
            .execute()
        
        if not teachers_response.data:
            raise HTTPException(status_code=404, detail="No teachers found for this course")
        
        # Format response
        teachers = []
        for teacher in teachers_response.data:
            teachers.append({
                "user_id": teacher["teacher_id"],
                "full_name": teacher["users"]["full_name"] if teacher["users"] else None
            })
        
        return teachers
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch course teachers: {str(e)}")


@router.get("/internal-messages/raise-hand-notifications")
async def get_raise_hand_notifications(user_id: str = Depends(authenticate_user)):
    """
    Fetch all active raise hand notifications for teachers.
    Filters by description.type = 'raise_hand' and excludes deleted notifications.
    """
    try:
        # Verify that the user is a teacher
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        if user_role != "Teacher":
            raise HTTPException(status_code=403, detail="Only teachers can view raise hand notifications")
        
        # Query raise hand notifications with course information
        notifications_response = supabase.from_("notifications") \
            .select("*, courses(course_title)") \
            .eq("creator_user_id", user_id) \
            .is_("deleted_at", None) \
            .execute()
        
        # Filter for raise hand notifications and add course_title to each notification
        raise_hand_notifications = []
        for notification in notifications_response.data:
            description = notification.get("description", {})
            if isinstance(description, dict) and description.get("type") == "raise_hand":
                # Add course_title to the notification object
                if notification.get("courses") and notification["courses"].get("course_title"):
                    notification["course_title"] = notification["courses"]["course_title"]
                else:
                    # Fallback to description.course_title if available
                    notification["course_title"] = description.get("course_title", "Unknown Course")
                
                # Remove the courses object as it's no longer needed
                notification.pop("courses", None)
                raise_hand_notifications.append(notification)
        
        return raise_hand_notifications
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch raise hand notifications: {str(e)}")


@router.delete("/internal-messages/raise-hand-notification/{notification_id}")
async def dismiss_raise_hand_notification(
    notification_id: str, 
    user_id: str = Depends(authenticate_user)
):
    """
    Dismiss a raise hand notification by marking it as deleted.
    """
    try:
        # Verify that the user is a teacher
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        if user_role != "Teacher":
            raise HTTPException(status_code=403, detail="Only teachers can dismiss notifications")
        
        # Verify the notification belongs to this teacher
        notification_response = supabase.from_("notifications") \
            .select("creator_user_id") \
            .eq("notification_id", notification_id) \
            .execute()
        
        if not notification_response.data:
            raise HTTPException(status_code=404, detail="Notification not found")
        
        if notification_response.data[0]["creator_user_id"] != user_id:
            raise HTTPException(status_code=403, detail="Access denied to this notification")
        
        # Mark notification as deleted
        delete_response = supabase.from_("notifications") \
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()}) \
            .eq("notification_id", notification_id) \
            .execute()
        
        if not delete_response.data:
            raise HTTPException(status_code=500, detail="Failed to dismiss notification")
        
        return {"success": True}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to dismiss notification: {str(e)}")


@router.get("/teacher-reply-notifications") 
async def get_teacher_reply_notifications(user_id: str = Depends(authenticate_user)):
    """
    Fetch all active teacher reply notifications for students.
    Filters by description.type = 'teacher_reply' and excludes deleted notifications.
    """
    try:
        # Verify that the user is a student
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        if user_role != "Student":
            raise HTTPException(status_code=403, detail="Only students can view teacher reply notifications")
        
        # Query teacher reply notifications for this student from notifications table
        # where creator_user_id = student_id (for student-targeted notifications)
        notifications_response = supabase.from_("notifications") \
            .select("*") \
            .eq("creator_user_id", user_id) \
            .is_("deleted_at", None) \
            .execute()
        
        # Filter for teacher reply notifications and ensure proper structure
        teacher_reply_notifications = []
        for notification in notifications_response.data:
            description = notification.get("description", {})
            if isinstance(description, dict) and description.get("type") == "teacher_reply":
                # Add course_title to the top level for frontend compatibility
                notification["course_title"] = description.get("course_title", "Unknown Course")
                teacher_reply_notifications.append(notification)
        
        return teacher_reply_notifications
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to fetch teacher reply notifications: {str(e)}")


@router.get("/internal-messages/unsent-to-chatbot")
async def get_unsent_internal_messages(
    session_id: str = Query(...), 
    user_id: str = Depends(authenticate_user)
):
    """
    Fetch internal messages that have not been sent to chatbot for a specific session.
    This is used when students send new messages to automatically include teacher-student discussions.
    """
    try:
        # Verify that the user has access to this session
        session_response = supabase.from_("chat_sessions_v2").select("user_id").eq("session_id", session_id).execute()
        if not session_response.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session_owner = session_response.data[0]["user_id"]
        
        # Get user role to determine access permissions
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        # Students can only access their own sessions, teachers can access any session
        if user_role == "Student" and session_owner != user_id:
            raise HTTPException(status_code=403, detail="Access denied to this session")
        
        # Query internal messages that haven't been sent to chatbot
        messages_response = supabase.from_("internal_messages") \
            .select("message_id, session_id, sender_user_id, receiver_user_id, message_content, message_type, is_read, is_sent_to_chatbot, created_at") \
            .eq("session_id", session_id) \
            .eq("is_sent_to_chatbot", False) \
            .order("created_at") \
            .execute()
        
        if not messages_response.data:
            return []
        
        # Get unique user IDs from messages
        user_ids = set()
        for message in messages_response.data:
            user_ids.add(message["sender_user_id"])
            user_ids.add(message["receiver_user_id"])
        
        # Fetch user roles for all involved users
        users_response = supabase.from_("users") \
            .select("user_id, role_name") \
            .in_("user_id", list(user_ids)) \
            .execute()
        
        # Create a mapping of user_id to role_name
        user_roles = {}
        if users_response.data:
            for user in users_response.data:
                user_roles[user["user_id"]] = user["role_name"]
        
        # Transform the response to include sender_role and receiver_role
        formatted_messages = []
        for message in messages_response.data:
            formatted_message = {
                "message_id": message["message_id"],
                "session_id": message["session_id"],
                "sender_user_id": message["sender_user_id"],
                "receiver_user_id": message["receiver_user_id"],
                "message_content": message["message_content"],
                "message_type": message["message_type"],
                "is_read": message["is_read"],
                "is_sent_to_chatbot": message["is_sent_to_chatbot"],
                "created_at": message["created_at"],
                "sender_role": user_roles.get(message["sender_user_id"]),
                "receiver_role": user_roles.get(message["receiver_user_id"])
            }
            formatted_messages.append(formatted_message)
        
        return formatted_messages
        
    except HTTPException:
        # Re-raise HTTP exceptions as they are
        raise
    except Exception as e:
        print(f"Unexpected error in get_unsent_internal_messages: {str(e)}")
        print(f"Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Failed to fetch unsent messages: {str(e)}")


@router.put("/internal-messages/mark-all-sent-to-chatbot")
async def mark_all_messages_sent_to_chatbot(
    session_id: str = Query(...), 
    user_id: str = Depends(authenticate_user)
):
    """
    Mark all internal messages in a session as sent to chatbot.
    This is called after student sends a new message that includes teacher-student discussions.
    """
    try:
        # Verify that the user has access to this session
        session_response = supabase.from_("chat_sessions_v2").select("user_id").eq("session_id", session_id).execute()
        if not session_response.data:
            raise HTTPException(status_code=404, detail="Session not found")
        
        session_owner = session_response.data[0]["user_id"]
        
        # Get user role to determine permissions
        user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
        if not user_response.data:
            raise HTTPException(status_code=404, detail="User not found")
        
        user_role = user_response.data[0]["role_name"]
        
        # Only the session owner (student) can mark messages as sent to chatbot
        if session_owner != user_id:
            raise HTTPException(status_code=403, detail="Access denied to this session")
        
        # Update all unsent messages as sent to chatbot
        update_response = supabase.from_("internal_messages") \
            .update({"is_sent_to_chatbot": True}) \
            .eq("session_id", session_id) \
            .eq("is_sent_to_chatbot", False) \
            .execute()
        
        updated_count = len(update_response.data) if update_response.data else 0
        
        return {
            "success": True,
            "updated_count": updated_count
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to mark messages as sent to chatbot: {str(e)}")
    