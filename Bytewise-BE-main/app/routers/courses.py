from fastapi import APIRouter, Depends, HTTPException, Query
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["courses"],
)


def unique_courses(courses):
    seen = set()
    unique_courses_list = []
    for course in courses:
        course_id = course['courses']['course_id']
        if course_id not in seen:
            seen.add(course_id)
            if not course['courses'].get('deleted_at'):
                unique_courses_list.append(course)
    return unique_courses_list


@router.get("/course-list")
async def get_course_list(user_id: str = Depends(authenticate_user)):
    # Getting information about the course
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    response = None
    course_data = []
    # Query course information according to user roles
    if user_role == "Student":
        # Student Role: Enquire about a student's chosen course
        enrollment_response = supabase \
            .from_("enrollments") \
            .select("group_id, deleted_at") \
            .eq("student_id", user_id) \
            .execute()
        if not enrollment_response.data:
            return []

        # Filter the deleted_at field
        group_ids = [enrollment["group_id"] for enrollment in enrollment_response.data if not enrollment["deleted_at"]]

        response = supabase.from_("groups_courses").select("course_id, courses(*)").in_(
            "group_id", group_ids).execute()

        # Filter the deleted_at field
        course_data = [course for course in response.data if not course["courses"]["deleted_at"]]
        # Make sure there are no duplicate courses
        course_data = unique_courses(course_data)
    elif user_role == "Teacher":
        # Teacher Role: Enquire about courses taught by teachers
        response = supabase.from_("groups_courses") \
            .select("course_id, teacher_id, courses(*)") \
            .eq("teacher_id", user_id) \
            .execute()
        # Filter the courses that are not teacher's courses
        course_data = [course for course in response.data if course["teacher_id"] == user_id]
        # Filter the deleted_at field
        course_data = [course for course in course_data if not course["courses"]["deleted_at"]]

    if not response.data:
        return []

    return course_data


@router.get("/course-info")
async def get_course_info(course_id: str = Query(...)):
    response = supabase.from_("courses").select("*").eq("course_id", course_id).execute()

    if not response.data:
        return []

    course_data = response.data[0]
    return course_data


@router.get("/course-student-list")
async def get_course_student_list(course_id: str = Query(...)):
    # Getting information about the course
    response = supabase.from_("course_student_item")\
        .select("*")\
        .eq("course_id", course_id)\
        .execute()
    if not response.data:
        return []

    # Filter the deleted_at field (deleted_at field means the student is unenrolled)
    student_data = [student for student in response.data if not student["enrollment_deleted_at"]]
    # Filter the null student_id field (no student_id means there is no student enrolled)
    student_data = [student for student in student_data if student["student_id"]]

    if not response.data:
        return []

    return student_data


class AddCourseData(BaseModel):
    course_title: str
    group_id: str
    start_date: str
    end_date: str


@router.post("/course")
async def add_course(course_data: AddCourseData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Add a course according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=404, detail="Student cannot add course")
    elif user_role == "Teacher":
        course_response = supabase.from_("courses").insert([{
            "creator_user_id": user_id,
            "course_title": course_data.course_title,
        }]).execute()

        if not course_response.data:
            return []

        course_id = course_response.data[0]["course_id"]

        groups_courses_response = supabase.from_("groups_courses").insert([{
            "group_id": course_data.group_id,
            "course_id": course_id,
            "teacher_id": user_id,
            "start_date": course_data.start_date,
            "end_date": course_data.end_date,
        }]).execute()

        if not groups_courses_response.data:
            return []

        # Insert a default module for the course
        response = supabase.from_("modules") \
            .insert([{
            "course_id": course_id,
            "module_title": "Default Module"
        }]) \
            .execute()
    else:
        return []

    if not response.data:
        return []

    return response.data[0]


class RenameCourseData(BaseModel):
    course_id: str
    course_title: str


@router.put("/course")
async def rename_course(course_data: RenameCourseData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Update course information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=404, detail="Student cannot rename course")
    elif user_role == "Teacher":
        response = supabase.from_("courses")\
            .update({"course_title": course_data.course_title})\
            .eq("course_id", course_data.course_id)\
            .execute()
    else:
        return []

    if not response.data:
        return []

    return response.data[0]


@router.delete("/course")
async def delete_course(course_id: str, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Delete course information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=404, detail="Student cannot delete course")
    elif user_role == "Teacher":
        # Update the course's deleted_at field to mark it as deleted
        course_response = supabase.from_("courses")\
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()})\
            .eq("course_id", course_id)\
            .execute()

        # Update the groups_courses's deleted_at field to mark it as deleted
        response = supabase.from_("groups_courses")\
            .update({"deleted_at": datetime.datetime.utcnow().isoformat()})\
            .eq("course_id", course_id)\
            .execute()
    else:
        return []

    if not response.data:
        return []

    return response.data[0]
