<template>
  <q-page v-if="!isFetching" class="q-pa-md">
    <div class="text-h3 q-pa-md">Script Editor</div>

    <q-separator spaced />

    <div class="q-pa-md">
      <div class="text-h6 q-mb-sm">Script Content</div>

      <q-inner-loading :showing="loading" />

      <q-banner v-if="error" class="bg-negative text-white">
        {{ error }}
      </q-banner>

      <template v-if="scriptData">
        <div v-for="(script, key) in scriptData" :key="key" class="q-mb-lg">
          <div class="text-subtitle1 text-weight-medium q-mb-sm">{{ key }}</div>
          <q-card flat bordered>
            <q-card-section>
              <q-input v-model="scriptData[key]" type="textarea" autogrow class="script-content" />
            </q-card-section>
          </q-card>
        </div>

        <div class="q-mb-lg">
          <div class="text-subtitle1 text-weight-medium q-mb-sm">Video Status</div>
          <q-card flat bordered>
            <q-card-section>
              <div v-if="vimeoUrl === null" class="video-status">
                <p class="text-italic">Video generation has not started yet.</p>
              </div>
              <div v-else-if="vimeoUrl === 'generating'" class="video-status">
                <p class="text-italic">Video is being generated. Please check back later.</p>
                <q-spinner color="primary" size="2em" class="q-mt-sm" />
              </div>
              <div v-else class="video-status">
                <p>Your video is ready:</p>
                <a :href="vimeoUrl" target="_blank" class="text-primary">{{ vimeoUrl }}</a>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="q-mt-md row">
          <div class="col-auto q-pr-md">
            <q-btn color="primary" label="Save Changes" @click="updateScript" :disable="isSaving" />
          </div>
          <div class="col-auto">
            <q-btn
              color="primary"
              label="Generate Video"
              @click="generateVideo"
              :disable="isSaving"
            />
          </div>
        </div>
      </template>

      <q-banner v-else class="bg-grey-3"> No script content available for this session </q-banner>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { api } from 'boot/axios';
import type { AxiosError } from 'axios';
import { useQuasar } from 'quasar';
import { watch } from 'vue';

interface ScriptData {
  [key: string]: string;
}

interface ApiError {
  response?: {
    status?: number;
    data?: {
      message?: string;
    };
  };
  message?: string;
}

const route = useRoute();
const sessionId = ref<string>(route.query.sessionId as string);
const scriptData = ref<ScriptData | null>(null);
const vimeoUrl = ref<string | null>(null);
const loading = ref<boolean>(false);
const isSaving = ref<boolean>(false);
const error = ref<string | null>(null);
const isFetching = ref<boolean>(true);
const $q = useQuasar();
const intervalId = ref<number | null>(null);

const fetchScript = async () => {
  try {
    loading.value = true;
    error.value = null;

    const response = await api.get(`/get-script/${sessionId.value}`);

    if (response.data.status === 'success' && response.data.data) {
      scriptData.value = response.data.data;
      vimeoUrl.value = response.data.vimeo_url || null;
      if (vimeoUrl.value == 'generating' && intervalId.value == null) {
        intervalId.value = window.setInterval(() => {
          // 使用 void 明确忽略 Promise 返回值
          void fetchScript();
        }, 30000);
      }
      if (vimeoUrl.value != null) {
        isSaving.value = true;
      }
    } else {
      error.value = 'No script data received';
    }
  } catch (err: unknown) {
    const axiosError = err as AxiosError<ApiError>;
    if (axiosError.response?.status === 404) {
      error.value = 'Script not found';
    } else if (axiosError.response?.status === 403) {
      error.value = 'You are not authorized to view this script';
    } else {
      error.value = 'Failed to load script';
    }
  } finally {
    loading.value = false;
    isFetching.value = false;
  }
};

const updateScript = async () => {
  if (!scriptData.value || !sessionId.value) return;

  try {
    isSaving.value = true;
    error.value = null;

    const response = await api.post('/update-script', {
      session_id: sessionId.value,
      script: scriptData.value,
    });

    if (response.data.status !== 'success') {
      error.value = 'Failed to update script';
    } else {
      $q.notify({
        type: 'positive' as const,
        message: `Script updated successfully!`,
        position: 'top',
      });
    }
  } catch (err: unknown) {
    const axiosError = err as AxiosError<ApiError>;
    error.value = axiosError.response?.data?.message || 'Error updating script';
    console.error(axiosError);
  } finally {
    isSaving.value = false;
  }
};

const generateVideo = () => {
  if (!scriptData.value || !sessionId.value) return;
  $q.dialog({
    title: 'Confirm Generation',
    message:
      'Are you sure you want to submit the script and generate a video? You can not edit the script after submission. The generation process could take up to 10 minutes.',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    void (async () => {
      try {
        isSaving.value = true;
        error.value = null;
        vimeoUrl.value = 'generating'; // Set to 'generating' state

        const response = await api.post('/generate-video', {
          session_id: sessionId.value,
          script: scriptData.value,
        });

        if (response.data.status !== 'success') {
          error.value = 'Failed to update script';
          vimeoUrl.value = null; // Revert to 'not started' if failed
        } else {
          $q.notify({
            type: 'positive' as const,
            message: 'Video generation started! Check back later for your video.',
            position: 'top',
          });
          // Start polling
          if (intervalId.value == null) {
            intervalId.value = window.setInterval(() => {
              // 使用 void 明确忽略 Promise 返回值
              void fetchScript();
            }, 30000);
          }
        }
      } catch (err: unknown) {
        const axiosError = err as AxiosError<ApiError>;
        error.value = axiosError.response?.data?.message || 'Error updating script';
        vimeoUrl.value = null;
        console.error(axiosError);
      } finally {
        isSaving.value = false;
      }
    })();
  });
};

onMounted(async () => {
  if (!sessionId.value) {
    error.value = 'No session ID provided';
    isFetching.value = false;
    return;
  }
  await fetchScript();
});

watch(vimeoUrl, (newUrl) => {
  if (newUrl !== 'generating' && intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
});

onBeforeUnmount(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
    intervalId.value = null;
  }
});
</script>

<style scoped>
.script-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  line-height: 1.5;
}

.q-item {
  padding: 8px 0;
}

.video-status {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}
</style>
