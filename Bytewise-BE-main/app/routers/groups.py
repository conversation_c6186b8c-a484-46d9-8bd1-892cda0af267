from fastapi import APIRouter, Depends, HTTPException, Query
from ..dependencies import authenticate_user
from supabase import create_client, Client
from pydantic import BaseModel
import os
import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Supabase client
supabase_url: str = os.environ.get("SUPABASE_URL")
supabase_key: str = os.environ.get("SUPABASE_KEY")
supabase: Client = create_client(supabase_url, supabase_key)

router = APIRouter(
    prefix="/api",
    tags=["groups"],
)


@router.get("/joined-student-group-list")
async def get_joined_student_group_list(user_id: str = Depends(authenticate_user)):
    # Getting student group information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Query course information according to user roles
    if user_role == "Student":
        # Student Role: Query notifications for a student
        enrollments_response = supabase.from_("enrollments").select("*").eq("student_id", user_id).execute()

        if not enrollments_response.data:
            return []

        # Filter the deleted_at field
        group_ids = [enrollment["group_id"] for enrollment in enrollments_response.data if not enrollment["deleted_at"]]

        group_response = supabase.from_("groups").select("*").in_("group_id", group_ids).execute()

        if not group_response.data:
            return []

        # Combine group and course information
        group_data_dict = {group["group_id"]: group for group in group_response.data}

        combined_data = []
        for enrollment in enrollments_response.data:
            group_id = enrollment["group_id"]
            if group_id in group_data_dict:
                group_info = group_data_dict[group_id]
                info_data = {
                    "enrollment_id": enrollment["enrollment_id"],
                    "group_id": group_info["group_id"],
                    "group_name": group_info["group_name"],
                    "description": group_info["description"],
                }
                combined_data.append(info_data)

    elif user_role == "Teacher":
        # Teacher Role: Not available
        return []
    else:
        # Other roles: return nothing
        return []

    return combined_data


class UpdateGroupIDData(BaseModel):
    group_course_id: str
    group_id: str


@router.put("/group-course")
async def update_group_id(data: UpdateGroupIDData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Update group information according to user roles
    response = None
    if user_role == "Student":
        return []
    elif user_role == "Teacher":
        # Teacher Role: Update the group associated with the course
        response = supabase.from_("groups_courses") \
            .update({"group_id": data.group_id, "updated_at": datetime.datetime.utcnow().isoformat()}) \
            .eq("group_course_id", data.group_course_id) \
            .execute()

    if not response.data:
        return []

    return response.data


class CreateGroupData(BaseModel):
    group_name: str
    description_content: str


@router.post("/group")
async def create_group(data: CreateGroupData, user_id: str = Depends(authenticate_user)):
    # Getting user information
    user_response = supabase.from_("users").select("role_name").eq("user_id", user_id).execute()
    if not user_response.data:
        raise HTTPException(status_code=404, detail="User not found")

    user_role = user_response.data[0]["role_name"]

    # Create group information according to user roles
    response = None
    if user_role == "Student":
        raise HTTPException(status_code=404, detail="Student cannot create a group")
    elif user_role == "Teacher":
        # Teacher Role: Create a group associated with a course
        response = supabase.from_("groups").insert({
            "group_name": data.group_name,
            "description": {"content": data.description_content},
            "creator_user_id": user_id,
        }).execute()

    if not response.data:
        return []

    return response.data[0]


@router.get("/group")
async def get_group(group_id: str = Query(...)):
    # Query group information
    response = supabase.from_("groups").select("*").eq("group_id", group_id).execute()

    if not response.data:
        return []

    return response.data[0]


@router.get("/group-by-course")
async def get_group_by_course(course_id: str = Query(...)):
    # Query group information
    group_course_response = supabase.from_("groups_courses").select("*").eq("course_id", course_id).execute()

    if not group_course_response.data:
        raise HTTPException(status_code=404, detail="Group not found")

    group_id = group_course_response.data[0]["group_id"]

    response = supabase.from_("groups").select("*").eq("group_id", group_id).execute()

    if not response.data:
        raise HTTPException(status_code=404, detail="Group not found")

    return response.data[0]
