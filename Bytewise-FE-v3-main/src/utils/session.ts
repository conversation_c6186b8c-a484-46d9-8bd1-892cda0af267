interface APIFormatMessage {
  role: string;
  content: string;
}

const convertApiConversationToConversation = (response: APIFormatMessage[]) => {
  // Hide the System prompt from the conversation
  response = response.filter((message) => message.role !== 'system');
  // Convert the API format to the display format
  let idCounter = 1;
  return response.map((message) => {
    // Determine the name based on role
    let name;
    let sent;
    let textColor;
    let bgColor;
    if (message.role === 'assistant') {
      name = 'Chatbot';
      sent = false;
      textColor = 'black';
      bgColor = 'grey-4';
    } else if (message.role === 'user') {
      name = 'User';
      sent = true;
      textColor = 'white';
      bgColor = 'blue-7';
    } else {
      name = 'Unknown'; // Default name if not Chatbot or User
      sent = false;
      textColor = 'black';
      bgColor = 'grey-4';
    }

    // Ensure content is a string and preprocess LaTeX math equations
    let preprocessedMessage = String(message.content || '');
    preprocessedMessage = preprocessedMessage.replace(/\\\[/g, '$$$');
    preprocessedMessage = preprocessedMessage.replace(/\\\]/g, '$$$');

    // Identify the file name of the attached file in a message
    const fileNameRegex = /```file:(.*?)\n/;
    const fileNameResult = preprocessedMessage.match(fileNameRegex);
    if (fileNameResult) {
      const fileName = fileNameResult ? fileNameResult[1] : '';

      // Identify the contents of a file in a message and replaces it with a filename tag
      const contentWithFileName = preprocessedMessage.replace(
        /```file([\s\S]*)```/g,
        '**<File: ' + fileName + '>**',
      );

      return {
        id: idCounter++,
        text: contentWithFileName,
        name: name,
        avatar: name === 'Chatbot' ? 'avatar/chatbot.png' : 'avatar/user.png',
        sent: sent,
        textColor: textColor,
        bgColor: bgColor,
      };
    } else {
      return {
        id: idCounter++,
        text: preprocessedMessage,
        name: name,
        avatar: name === 'Chatbot' ? 'avatar/chatbot.png' : 'avatar/user.png',
        sent: sent,
        textColor: textColor,
        bgColor: bgColor,
      };
    }
  });
};

export { convertApiConversationToConversation };
