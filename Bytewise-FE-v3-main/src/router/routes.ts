import type { RouteRecordRaw, RouteLocationNormalized, NavigationGuardNext } from 'vue-router';

import StartPage from 'pages/StartPage.vue';
import RegisterPage from 'pages/RegisterPage.vue';
import LoginPage from 'pages/LoginPage.vue';
import StudentLayout from 'layouts/StudentLayout.vue';
import AccountPage from 'pages/AccountPage.vue';
import HomePage from 'pages/HomePage.vue';
import StudentCoursePage from 'pages/student/CoursePage.vue';
import TeacherLayout from 'layouts/TeacherLayout.vue';
import TeacherCoursePage from 'pages/teacher/CoursePage.vue';
import ChatLayout from 'layouts/ChatLayout.vue';
import SessionPage from 'pages/SessionPage.vue';
import ChatSharingPage from 'pages/ChatSharingPage.vue';
import ManageStudentPage from 'pages/teacher/ManageStudentPage.vue';
import CreateNewCoursePage from 'pages/teacher/CreateNewCoursePage.vue';
import ManageChatbotPage from 'pages/teacher/ManageChatbotPage.vue';
import CreateNewChatbotPage from 'pages/teacher/CreateNewChatbotPage.vue';
import SettingChatbotPage from 'pages/teacher/SettingChatbotPage.vue';
import ReviewChatbotUsagePage from 'pages/teacher/ReviewChatbotUsagePage.vue';
import ChatbotUsageLayout from 'layouts/ChatbotUsageLayout.vue';
import ChatbotUsageSessionPage from 'pages/teacher/ChatbotUsageSessionPage.vue';
import CreateNewNotificationPage from 'pages/teacher/CreateNewNotificationPage.vue';
import EnrollNewStudentGroupPage from 'pages/student/EnrollNewStudentGroupPage.vue';
import ActivationPage from 'src/pages/ActivationPage.vue';
// import AvatarLayoutTemp from 'layouts/AvatarLayoutTemp.vue';
import AvatarLayout from 'src/layouts/AvatarLayout.vue';
import AvatarSessionPage from 'pages/AvatarSessionPage.vue';
import AboutPage from 'pages/AboutPage.vue';
//newly added
import ManageAvatarPage from 'pages/teacher/ManageAvatarPage.vue';
import CreateNewAvatarPage from 'pages/teacher/CreateNewAvatarPage.vue';
import SettingAvatarPage from 'pages/teacher/SettingAvatarPage.vue';
import GenerateScript from 'src/pages/teacher/GenerateScript.vue';

const authGuard = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const token = localStorage.getItem('btws-tkn');
  if (!token) {
    next('/login');
  } else {
    next();
  }
};

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/start',
  },
  {
    path: '/start',
    component: StartPage,
  },
  {
    path: '/register',
    component: RegisterPage,
  },
  {
    path: '/login',
    component: LoginPage,
  },
  {
    path: '/activate/:token',
    component: ActivationPage,
  },
  {
    path: '/student',
    component: StudentLayout,
    beforeEnter: authGuard,
    children: [
      { path: 'homepage', component: HomePage },
      { path: 'account', component: AccountPage },
      { path: 'course/:courseId', component: StudentCoursePage },
    ],
  },
  {
    path: '/student/enroll/:groupId',
    component: EnrollNewStudentGroupPage,
    beforeEnter: authGuard,
  },
  {
    path: '/teacher',
    component: TeacherLayout,
    beforeEnter: authGuard,
    children: [
      { path: 'homepage', component: HomePage },
      { path: 'notification/new', component: CreateNewNotificationPage },
      { path: 'course/new', component: CreateNewCoursePage },
      { path: 'course/:courseId', component: TeacherCoursePage },
      { path: 'manage-chatbot', component: ManageChatbotPage },
      { path: 'chatbot/new', component: CreateNewChatbotPage },
      { path: 'chatbot/:chatbotId', component: SettingChatbotPage },
      { path: 'chatbot/:chatbotId/usage', component: ReviewChatbotUsagePage },
      { path: 'account', component: AccountPage },
      { path: 'manage-avatars', component: ManageAvatarPage },
      { path: 'avatar/new', component: CreateNewAvatarPage },
      { path: 'edit-avatar', component: SettingAvatarPage },
      { path: 'generate-script', component: GenerateScript },
    ],
  },
  {
    path: '/teacher/chatbot/:chatbotId/usage/:studentId',
    component: ChatbotUsageLayout,
    children: [{ path: 'session/:sessionId', component: ChatbotUsageSessionPage }],
  },
  {
    path: '/teacher/course/:courseId/manage-student',
    component: ManageStudentPage,
  },
  {
    path: '/teacher/avatar',
    component: AvatarLayout,
    beforeEnter: authGuard,
    children: [{ path: '', component: AvatarSessionPage }],
  },
  {
    path: '/chat/:chatbotId',
    component: ChatLayout,
    beforeEnter: authGuard,
    children: [{ path: 'session/:sessionId', component: SessionPage }],
  },
  {
    path: '/share/:sessionSharingId',
    component: ChatSharingPage,
  },
  {
    path: '/about',
    component: AboutPage,
  },
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
];

export default routes;
