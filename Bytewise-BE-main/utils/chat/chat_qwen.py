import json
import os
import time
import dashscope
from dotenv import load_dotenv

load_dotenv()
dashscope_api_key = os.environ.get("DASHSCOPE_API_KEY")


def parse_qwen_api_res(session_id, chatbot_id, res, time_elapsed):
    try:
        # Initialize an empty string for text and finish_reason if they are null.
        message_content = (res.get("output", {}).get("choices", [])[0].get("message", {}).get("content", "") or "")
        finish_reason = (res.get("output", {}).get("choices", [])[0].get("finish_reason", "") or "")

        # Prepare the record for database insertion.
        db_record = {
            "session_id": session_id,
            "chatbot_id": chatbot_id,
            "api_provider": "Tongyi Qwen",
            "api_id": res.get("request_id", ""),
            "api_message_content": message_content,
            "api_message_role": res.get("output", {}).get("choices", [])[0].get("message", {}).get("role", ""),
            "api_finish_reason": finish_reason,
            "api_prompt_tokens": res.get("usage", {}).get("input_tokens", 0),
            "api_completion_tokens": res.get("usage", {}).get("output_tokens", 0),
            "api_total_tokens": res.get("usage", {}).get("total_tokens", 0),
            "time_elapsed": time_elapsed
        }
    except AttributeError as e:
        print(f"AttributeError encountered: {e}. 'res' type: {type(res)}")
        # If the response is not in the expected dictionary format, return None.
        db_record = None
    except Exception as e:
        print(f"Exception encountered: {e}. 'res' type: {type(res)}")
        # If an unexpected exception occurs, return None.
        db_record = None

    return db_record


async def chat_by_qwen_api(conversation_list, model_name="qwen-turbo", temperature=0.7):
    print("Use Qwen API")

    start_time = time.time()

    # Generate grade by accessing Qwen API
    dashscope.api_key = dashscope_api_key

    res = await dashscope.AioGeneration.call(
        model=model_name,
        messages=conversation_list,
        result_format="message",
        temperature=temperature
    )
    chat_response = ""
    if res.status_code == 200:
        chat_response = res["output"]["choices"][0]["message"]["content"]
    else:
        print(res.code, res.message)

    end_time = time.time()
    time_elapsed = end_time - start_time
    print("Time Elapsed:", time_elapsed, "seconds")
    # print("Chat Response:", chat_response)
    # print("Response:", res)
    res_json = json.dumps(res, default=lambda o: o.__dict__)

    return chat_response, json.loads(res_json), time_elapsed
