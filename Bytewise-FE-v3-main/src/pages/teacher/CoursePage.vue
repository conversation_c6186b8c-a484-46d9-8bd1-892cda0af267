<template>
  <q-page v-if="!isFetching" class="q-pa-md">
    <div class="row items-center justify-between q-mb-lg">
      <div class="text-h3">{{ courseTitle }}</div>
      <div class="row q-gutter-sm" v-if="!isOpenCourses">
        <q-btn round color="primary" icon="edit" size="md" @click="renameCurrentCourse()">
          <q-tooltip>Rename this Course</q-tooltip>
        </q-btn>
        <q-btn round color="primary" icon="group" size="md" @click="manageStudent()">
          <q-tooltip>Manage Student Group</q-tooltip>
        </q-btn>
        <q-btn round color="primary" icon="add_box" size="md" @click="createNewModule()">
          <q-tooltip>Create a new Module</q-tooltip>
        </q-btn>
        <q-btn
          round
          color="negative"
          icon="delete"
          size="md"
          @click="deleteCurrentCourse()"
          v-if="moduleChatbotList.length === 0"
        >
          <q-tooltip>Delete this Course</q-tooltip>
        </q-btn>
        <q-btn flat round icon="info" color="grey" size="md" v-else>
          <q-tooltip>You can only delete this course if there are no modules available.</q-tooltip>
        </q-btn>
      </div>
    </div>

    <div v-if="moduleCombinedList.length === 0" class="text-h6 text-grey-6 text-center q-pa-xl">
      No modules available for this course
    </div>

    <div v-for="module in moduleCombinedList" :key="module.module_id" class="q-mb-xl">
      <q-separator spaced />

      <div class="row items-center justify-between q-py-md">
        <div class="text-h4">{{ module.module_title }}</div>
        <div class="row q-gutter-sm" v-if="!isOpenCourses">
          <q-btn round color="primary" icon="edit" size="sm" @click="renameCurrentModule(module)">
            <q-tooltip>Rename this Module</q-tooltip>
          </q-btn>
          <q-btn
            round
            color="primary"
            icon="add_circle"
            size="sm"
            @click="importNewChatbot(module)"
          >
            <q-tooltip>Import new Chatbot</q-tooltip>
          </q-btn>
          <q-btn round color="primary" icon="person_add" size="sm" @click="importNewAvatar(module)">
            <q-tooltip>Import new Avatar</q-tooltip>
          </q-btn>
          <q-btn
            round
            color="negative"
            icon="delete"
            size="sm"
            v-if="
              (module.chatbots.length === 1 &&
                !module.chatbots[0]?.chatbot_id &&
                !module.avatars[0]?.avatar_id) ||
              (module.chatbots.length === 0 && module.avatars.length === 0)
            "
            @click="deleteCurrentModule(module)"
          >
            <q-tooltip>Delete this Module</q-tooltip>
          </q-btn>
          <q-btn flat round icon="info" color="grey" size="sm" v-else>
            <q-tooltip
              >You can only delete this module if there are no chatbots or avatars
              available.</q-tooltip
            >
          </q-btn>
        </div>
      </div>

      <div
        v-if="
          (module.chatbots.length === 1 &&
            !module.chatbots[0]?.chatbot_id &&
            !module.avatars[0]?.avatar_id) ||
          (module.chatbots.length === 0 && module.avatars.length === 0)
        "
        class="text-subtitle1 text-grey-6 text-center q-pa-md"
      >
        No chatbots or avatars available for this module
      </div>

      <div class="row q-col-gutter-md">
        <div
          v-for="item in getValidChatbotsAndAvatars(module)"
          :key="item.type === 'chatbot' ? item.chatbot_id : item.avatar_id"
          class="col-12 col-sm-6 col-md-4"
        >
          <q-card flat bordered class="h-100">
            <q-card-section>
              <div class="text-h6 q-mb-md">
                {{ item.type === 'chatbot' ? item.chatbot_name : item.avatar_name }}
              </div>
              <div class="q-gutter-y-sm text-body2">
                <div class="row items-center">
                  <q-icon name="school" size="xs" class="q-mr-sm" />
                  <span>Course: {{ courseTitle }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="person" size="xs" class="q-mr-sm" />
                  <span>Teacher: {{ item.creator_user_full_name }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="event" size="xs" class="q-mr-sm" />
                  <span>Created on: {{ item.created_at }}</span>
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <q-card-actions align="center">
              <q-btn
                round
                color="primary"
                icon="chat"
                size="sm"
                @click="
                  continuePreviousChat(item.chatbot_id, module.module_id, module.module_title)
                "
                v-if="item.type === 'chatbot'"
              >
                <q-tooltip>Start to Chat</q-tooltip>
              </q-btn>
              <q-btn
                round
                color="primary"
                icon="chat"
                size="sm"
                v-if="item.type === 'avatar'"
                @click="jumpToAvatarChat(item.avatar_id)"
              >
                <q-tooltip>Start to Talk</q-tooltip>
              </q-btn>
              <q-btn
                round
                color="primary"
                icon="analytics"
                size="sm"
                @click="reviewChatbotUsage(module, item)"
                v-if="!isOpenCourses && item.type === 'chatbot'"
              >
                <q-tooltip>Review Chatbot Usage</q-tooltip>
              </q-btn>
              <q-btn
                round
                color="primary"
                icon="settings"
                size="sm"
                @click="handleSettingChatbot(item)"
                v-if="!isOpenCourses && item.type === 'chatbot'"
              >
                <q-tooltip>Chatbot Setting</q-tooltip>
              </q-btn>
              <q-btn
                round
                color="negative"
                icon="remove_circle"
                size="sm"
                @click="item.type === 'chatbot' ? removeCurrentChatbot(module, item) : null"
                v-if="!isOpenCourses && item.type === 'chatbot'"
              >
                <q-tooltip>Remove this Chatbot</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { api } from 'boot/axios';
import { onMounted } from 'vue';
import { useQuasar } from 'quasar';

const route = useRoute();
const router = useRouter();
const $q = useQuasar();
interface AvatarItem {
  avatar_id: string;
  id: string;
  avatar_name: string;
  created_at: string;
  creator_user_id: string;
  creator_user_full_name: string;
}

interface ChatbotItem {
  chatbot_id: string;
  chatbot_name: string;
  created_at: string;
  creator_user_id: string;
  creator_user_full_name: string;
}
interface ModuleAvatarItem {
  module_id: string;
  course_id: string;
  module_title: string;
  description: JSON;
  created_at: string;
  avatars: AvatarItem[];
}

interface ModuleChatbotItem {
  module_id: string;
  course_id: string;
  module_title: string;
  description: JSON;
  created_at: string;
  chatbots: ChatbotItem[];
}

interface ModuleCombinedItem {
  module_id: string;
  course_id: string;
  module_title: string;
  description: JSON;
  created_at: string;
  chatbots: ChatbotItem[];
  avatars: AvatarItem[];
}

const moduleChatbotList = ref<ModuleChatbotItem[]>([]);
const moduleAvatarList = ref<ModuleAvatarItem[]>([]);
const moduleCombinedList = ref<ModuleCombinedItem[]>([]);

const isFetching = ref<boolean>(true);

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const courseId = ref<string>(getStringParam(route.params.courseId || ''));

const courseTitle = ref<string>('');
const isOpenCourses = ref<boolean>(false);

const manageStudent = async () => {
  await router.push(`/teacher/course/${courseId.value}/manage-student`);
};

const renameCurrentCourse = () => {
  try {
    $q.dialog({
      title: 'Rename Course',
      message: 'Enter the new title of the course',
      prompt: {
        model: courseTitle.value,
        isValid: (val: string) => val.trim().length > 0,
        type: 'text',
      },
      persistent: true,
      ok: 'Rename',
      cancel: 'Cancel',
    }).onOk((data) => {
      void (async () => {
        // Rename this course
        await api.put('/course', {
          course_id: courseId.value,
          course_title: data,
        });
        // Update the courseTitle with the new title
        courseTitle.value = data;
        // Notify the user about the successful renaming
        $q.notify({
          type: 'positive',
          message: 'Course renamed successfully',
        });
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to rename the course: ' + String(error),
    });
  }
};

const deleteCurrentCourse = () => {
  try {
    $q.dialog({
      title: 'Delete Course',
      message:
        'Are you sure you want to delete this course (' +
        courseTitle.value +
        ')? Note: All information related to this course will be deleted except the Student Group. You can go to the Manage Student Group page to collect the group_id before deleting the course.',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete this course
        await api.delete('/course', {
          params: {
            course_id: courseId.value,
          },
        });
        // Notify the user about the successful deletion
        $q.notify({
          type: 'positive',
          message: 'Course (' + courseTitle.value + ') deleted successfully',
        });
        // Redirect to the teacher home page
        await router.push('/teacher/homepage');
        // Force refresh the page
        window.location.reload();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to delete the course: ' + String(error),
    });
  }
};

const createNewModule = () => {
  try {
    $q.dialog({
      title: 'Create a new Module',
      message: 'Enter the title of the new module',
      prompt: {
        model: '',
        isValid: (val: string) => val.trim().length > 0,
        type: 'text',
      },
      persistent: true,
      ok: 'Create',
      cancel: 'Cancel',
    }).onOk((data) => {
      void (async () => {
        // Create a new module
        await api.post('/module', {
          course_id: courseId.value,
          module_title: data,
        });
        // Notify the user about the successful creation
        $q.notify({
          type: 'positive',
          message: 'New module created successfully',
        });
        // Fetch the updated module list
        await fetchModuleChatbotList();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to create a new module: ' + String(error),
    });
  }
};

const renameCurrentModule = (module: ModuleChatbotItem) => {
  try {
    $q.dialog({
      title: 'Rename Module',
      message: 'Enter the new title of the module',
      prompt: {
        model: module.module_title,
        isValid: (val: string) => val.trim().length > 0,
        type: 'text',
      },
      persistent: true,
      ok: 'Rename',
      cancel: 'Cancel',
    }).onOk((data) => {
      void (async () => {
        // Rename this module
        await api.put('/module', {
          module_id: module.module_id,
          module_title: data,
        });
        // Notify the user about the successful renaming
        $q.notify({
          type: 'positive',
          message: 'Module renamed successfully',
        });
        // Fetch the updated module list
        await fetchModuleChatbotList();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to rename the module: ' + String(error),
    });
  }
};

const deleteCurrentModule = (module: ModuleChatbotItem) => {
  try {
    $q.dialog({
      title: 'Delete Module',
      message: 'Are you sure you want to delete this module (' + module.module_title + ')?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete this module
        await api.delete('/module', {
          params: {
            module_id: module.module_id,
          },
        });
        // console.log(response.data);
        // Notify the user about the successful deletion
        $q.notify({
          type: 'positive',
          message: 'Module (' + module.module_title + ') deleted successfully',
        });
        // Fetch the updated module list
        await fetchModuleChatbotList();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to delete the module: ' + String(error),
    });
  }
};
const importNewAvatar = async (module: ModuleChatbotItem) => {
  try {
    // Get the list of chatbots available for import
    const response = await api.get('/avatar-list-by-module', {
      params: {
        module_id: module.module_id,
      },
    });
    // Sort the avatars by created_at from large to small
    response.data.sort(
      (a: AvatarItem, b: AvatarItem) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    );
    // If there is no chatbot available for import
    if (response.data.length === 0) {
      $q.notify({
        type: 'negative',
        message:
          'No chatbot available for import, please create a new chatbot on the Chatbot page first.',
      });
      return;
    }
    // Show the dialog to select a chatbot to import
    $q.dialog({
      title: 'Import new Chatbot',
      message: 'Select chatbots to import',
      options: {
        type: 'checkbox',
        model: [],
        items: response.data.map((avatar: AvatarItem) => {
          return {
            label: avatar.avatar_name,
            value: avatar.id,
          };
        }),
      },
      cancel: 'Cancel',
    }).onOk((data) => {
      void (async () => {
        if (data.length === 0) {
          $q.notify({
            type: 'negative',
            message: 'No avatar selected for import',
          });
          return;
        }
        // Import the selected chatbots
        await api.post('/module-avatars', {
          module_id: module.module_id,
          avatar_ids: data,
        });
        // Notify the user about the successful import
        $q.notify({
          type: 'positive',
          message:
            data.length +
            ' chatbot imported successfully to the module (' +
            module.module_title +
            ')',
        });
        // Fetch the updated module list
        await fetchModuleAvatarList();
        moduleCombinedList.value = combineModuleLists(
          moduleChatbotList.value,
          moduleAvatarList.value,
        );
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to import chatbot: ' + String(error),
    });
  }
};

function combineModuleLists(
  moduleChatbotList: ModuleChatbotItem[],
  moduleAvatarList: ModuleAvatarItem[],
) {
  // Step 1: Create a Map for fast lookup of avatars by module_id
  const avatarMap = new Map();
  moduleAvatarList.forEach((module) => {
    avatarMap.set(module.module_id, module);
  });

  // Step 2: Process each module in moduleChatbotList
  const result = moduleChatbotList.map((module) => {
    const avatarModule = avatarMap.get(module.module_id);
    return {
      module_id: module.module_id ?? null,
      course_id: module.course_id ?? null,
      module_title: module.module_title ?? null,
      description: module.description ?? null,
      created_at: module.created_at ?? null,
      chatbots:
        Array.isArray(module.chatbots) && module.chatbots.length > 0
          ? module.chatbots.map((chatbot) => ({
              chatbot_id: chatbot.chatbot_id ?? null,
              chatbot_name: chatbot.chatbot_name ?? null,
              created_at: chatbot.created_at ?? null,
              creator_user_id: chatbot.creator_user_id ?? null,
              creator_user_full_name: chatbot.creator_user_full_name ?? null,
            }))
          : [],
      avatars:
        avatarModule && Array.isArray(avatarModule.avatars) && avatarModule.avatars.length > 0
          ? avatarModule.avatars.map((avatar: AvatarItem) => ({
              avatar_id: avatar.avatar_id ?? null,
              avatar_name: avatar.avatar_name ?? null,
              created_at: avatar.created_at ?? null,
              creator_user_id: avatar.creator_user_id ?? null,
              creator_user_full_name: avatar.creator_user_full_name ?? null,
            }))
          : [],
    };
  });

  // Step 3: Add modules that are only in moduleAvatarList
  moduleAvatarList.forEach((module) => {
    if (!moduleChatbotList.some((m) => m.module_id === module.module_id)) {
      result.push({
        module_id: module.module_id ?? null,
        course_id: module.course_id ?? null,
        module_title: '',
        description: {} as JSON,
        created_at: '',
        chatbots: [],
        avatars:
          Array.isArray(module.avatars) && module.avatars.length > 0
            ? module.avatars.map((avatar) => ({
                avatar_id: avatar.avatar_id ?? null,
                avatar_name: avatar.avatar_name ?? null,
                created_at: avatar.created_at ?? null,
                creator_user_id: avatar.creator_user_id ?? null,
                creator_user_full_name: avatar.creator_user_full_name ?? null,
              }))
            : [],
      });
    }
  });

  return result;
}

const jumpToAvatarChat = async (avatar_id: string) => {
  await router.push('/teacher/avatar?id=' + avatar_id);
};

const fetchModuleAvatarList = async () => {
  // Fetch course modules and chatbots from the server
  const response = await api.get('/module-avatar-list', {
    params: {
      course_id: courseId.value,
    },
  });
  // Update the moduleChatbotList with the fetched data
  moduleAvatarList.value = response.data;

  moduleAvatarList.value.sort(
    (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
  );
  moduleAvatarList.value.forEach((module) => {
    module.avatars.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );
  });
  // Convert UTC time to local time
  moduleAvatarList.value.forEach((module) => {
    module.avatars.forEach((avatar) => {
      avatar.created_at = utcTimeToLocalTime(avatar.created_at);
    });
  });
};
const importNewChatbot = async (module: ModuleChatbotItem) => {
  try {
    // Get the list of chatbots available for import
    const response = await api.get('/chatbot-list-by-module', {
      params: {
        module_id: module.module_id,
      },
    });
    // console.log(response.data);
    // Sort the chatbots by created_at from large to small
    response.data.sort(
      (a: ChatbotItem, b: ChatbotItem) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    );
    // If there is no chatbot available for import
    if (response.data.length === 0) {
      $q.notify({
        type: 'negative',
        message:
          'No chatbot available for import, please create a new chatbot on the Chatbot page first.',
      });
      return;
    }
    // Show the dialog to select a chatbot to import
    $q.dialog({
      title: 'Import new Chatbot',
      message: 'Select chatbots to import',
      options: {
        type: 'checkbox',
        model: [],
        items: response.data.map((chatbot: ChatbotItem) => {
          return {
            label: chatbot.chatbot_name,
            value: chatbot.chatbot_id,
          };
        }),
      },
      cancel: 'Cancel',
    }).onOk((data) => {
      void (async () => {
        if (data.length === 0) {
          $q.notify({
            type: 'negative',
            message: 'No chatbot selected for import',
          });
          return;
        }
        // console.log(data);
        // Import the selected chatbots
        await api.post('/module-chatbots', {
          module_id: module.module_id,
          chatbot_ids: data,
        });
        // Notify the user about the successful import
        $q.notify({
          type: 'positive',
          message:
            data.length +
            ' chatbot imported successfully to the module (' +
            module.module_title +
            ')',
        });
        // Fetch the updated module list
        await fetchModuleChatbotList();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to import chatbot: ' + String(error),
    });
  }
};

// const startNewChat = (chatbot_id: string, module_id: string, module_title: string) => {
//   router.push({
//     path: `/chat/${chatbot_id}/session/new`, query: {
//       courseTitle: courseTitle.value,
//       moduleId: module_id,
//       moduleTitle: module_title
//     }
//   });
// };

const continuePreviousChat = async (
  chatbot_id: string,
  module_id: string,
  module_title: string,
) => {
  await router.push({
    path: `/chat/${chatbot_id}/session/latest`,
    query: {
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: module_id,
      moduleTitle: module_title,
    },
  });
};

const reviewChatbotUsage = async (module: ModuleChatbotItem, chatbot: ChatbotItem) => {
  await router.push({
    path: `/teacher/chatbot/${chatbot.chatbot_id}/usage`,
    query: {
      chatbotName: chatbot.chatbot_name,
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: module.module_id,
      moduleTitle: module.module_title,
    },
  });
};

const removeCurrentChatbot = (module: ModuleChatbotItem, chatbot: ChatbotItem) => {
  try {
    $q.dialog({
      title: 'Remove Chatbot',
      message: 'Are you sure you want to remove this chatbot (' + chatbot.chatbot_name + ')?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete this chatbot
        await api.delete('/module-chatbot', {
          params: {
            module_id: module.module_id,
            chatbot_id: chatbot.chatbot_id,
          },
        });
        // Notify the user about the successful deletion
        $q.notify({
          type: 'positive',
          message: 'Chatbot (' + chatbot.chatbot_name + ') removed successfully',
        });
        // Fetch the updated module list
        await fetchModuleChatbotList();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to remove the chatbot: ' + String(error),
    });
  }
};

const handleSettingChatbot = async (chatbot: ChatbotItem) => {
  await router.push({
    path: '/teacher/chatbot/' + chatbot.chatbot_id,
    query: {
      source: 'course',
      courseId: courseId.value,
    },
  });
};

const utcTimeToLocalTime = (utcTime: string): string => {
  return new Date(utcTime).toLocaleString();
};

const fetchCourseInfo = async () => {
  // Reset the isOpenCourses flag
  isOpenCourses.value = false;
  // Fetch course information from the server
  const response = await api.get('/course-info', {
    params: {
      course_id: courseId.value,
    },
  });
  // console.log(response.data);
  // Update the courseTitle with the fetched data
  courseTitle.value = response.data.course_title;
  // Check if the course is the Open Courses (Provided by the admin)
  if (response.data.course_id === '0b99ca04-70c7-47cc-98c5-c39e6fbad057') {
    isOpenCourses.value = true;
  }
};

const fetchModuleChatbotList = async () => {
  // Fetch course modules and chatbots from the server
  const response = await api.get('/module-chatbot-list', {
    params: {
      course_id: courseId.value,
    },
  });
  // console.log(response.data);
  // Update the moduleChatbotList with the fetched data
  moduleChatbotList.value = response.data;

  // Sort the Modules and Chatbots by created_at from small to large
  moduleChatbotList.value.sort(
    (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
  );
  moduleChatbotList.value.forEach((module) => {
    module.chatbots.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );
  });
  // Convert UTC time to local time
  moduleChatbotList.value.forEach((module) => {
    module.chatbots.forEach((chatbot) => {
      chatbot.created_at = utcTimeToLocalTime(chatbot.created_at);
    });
  });
};

// 添加一个函数来过滤有效的聊天机器人
// const getValidChatbots = (module: ModuleChatbotItem): ChatbotItem[] => {
//   return module.chatbots.filter((chatbot) => chatbot.chatbot_id);
// };
type TaggedChatbot = ChatbotItem & { type: 'chatbot' };
type TaggedAvatar = AvatarItem & { type: 'avatar' };
type CombinedItem = TaggedChatbot | TaggedAvatar;

const getValidChatbotsAndAvatars = (module: ModuleCombinedItem): CombinedItem[] => {
  const validChatbots = module.chatbots
    .filter((chatbot) => chatbot.chatbot_id)
    .map((chatbot) => ({ ...chatbot, type: 'chatbot' as const }));
  const validAvatars = module.avatars
    .filter((avatar) => avatar.avatar_id)
    .map((avatar) => ({ ...avatar, type: 'avatar' as const }));
  return [...validChatbots, ...validAvatars];
};

watch(route, async (newRoute) => {
  isFetching.value = true;
  courseId.value = getStringParam(newRoute.params.courseId || '');

  await fetchCourseInfo();
  await Promise.all([fetchModuleChatbotList(), fetchModuleAvatarList()]);
  moduleCombinedList.value = combineModuleLists(moduleChatbotList.value, moduleAvatarList.value);
  isFetching.value = false;
});

onMounted(async () => {
  isFetching.value = true;
  await fetchCourseInfo();
  await Promise.all([fetchModuleChatbotList(), fetchModuleAvatarList()]);
  moduleCombinedList.value = combineModuleLists(moduleChatbotList.value, moduleAvatarList.value);
  isFetching.value = false;
});


</script>

<style scoped>
.h-100 {
  height: 100%;
}
</style>
