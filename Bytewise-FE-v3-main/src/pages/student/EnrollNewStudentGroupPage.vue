<template>
  <q-layout view="hHh lpR fFf">
    <!-- Top Bar -->
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn
            flat
            no-caps
            label="Bytewise"
            :to="urlToHomepage(user.role_name)"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 5px"
          />
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container>
      <q-page v-if="!isFetching">
        <div class="q-banner text-h6 q-pa-md row q-gutter-sm">
          <div>Enroll a new Student Group - {{ groupName }}</div>
        </div>
        <div class="q-pa-md column items-center justify-evenly">
          <div class="text-h6">
            This link invites you to enroll in a new student group: {{ groupName }}
          </div>
          <q-btn
            no-caps
            label="Click to Enroll"
            color="black"
            text-color="white"
            @click="handleEnrollNewStudentGroup"
          >
            <q-tooltip>Enroll the new student group</q-tooltip>
          </q-btn>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const $q = useQuasar();
const router = useRouter();
const route = useRoute();

const urlToHomepage = (role: string) => {
  if (role === 'Teacher') {
    return '/teacher/homepage';
  } else if (role === 'Student') {
    return '/student/homepage';
  } else {
    return '/';
  }
};

const isFetching = ref(true);

const groupId = ref<string>('');
const groupName = ref<string>('');

interface User {
  user_id: string;
  username: string;
  full_name: string;
  email: string;
  role_name: string;
  personnel_id: string;
}

const user = ref<User>({
  user_id: '',
  username: '',
  full_name: '',
  email: '',
  role_name: '',
  personnel_id: '',
});

const handleEnrollNewStudentGroup = async () => {
  // Enroll the new student group
  try {
    // Enroll the current user to the new student group
    await api.post('/email-enrollment', {
      group_id: groupId.value,
      email: user.value.email,
    });
    // console.log(response.data);
    $q.notify({
      type: 'positive',
      message: 'Successfully enrolled in the new student group',
    });
    // Redirect to the Homepage
    await router.push('/student/homepage');
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to enroll: ' + String(error),
    });
  }
};

const fetchAccountInfo = async () => {
  // Fetch notification list from the server
  const response = await api.get('/user-info');
  // console.log(response.data);
  if (response.data.role_name !== 'Student') {
    $q.notify({
      type: 'negative',
      message: 'You are not a student, please login as a student to enroll in a student group.',
    });
    // Redirect to the Homepage
    await router.push('/student/homepage');
  }
  // Update the notificationList with the fetched data
  user.value = response.data;
};

const fetchGroupInfo = async (groupId: string) => {
  // Fetch group info from the server
  const response = await api.get('/group', {
    params: {
      group_id: groupId,
    },
  });
  // console.log(response.data);
  // Update the student group information
  groupName.value = response.data.group_name;
};

onMounted(async () => {
  isFetching.value = true;
  groupId.value = getStringParam(route.params.groupId || '');
  await fetchAccountInfo();
  await fetchGroupInfo(groupId.value);
  isFetching.value = false;
});
</script>

<style scoped>
.q-drawer {
  width: 300px;
}
</style>
