<template>
  <q-layout view="hHh LpR lfr">
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn
            flat
            no-caps
            label="Bytewise"
            to="/teacher/homepage"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 5px"
          />
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-page-container>
      <div class="text-center q-pa-md">
        <h5>Bytewise Avatar Demo</h5>
        <p>Click the button below to open Avatar Demo in a new window</p>
        <div class="q-gutter-md">
          <q-btn
            no-caps
            color="primary"
            label="Open Avatar Demo"
            @click="openAvatarService"
            icon="open_in_new"
          />
          <q-btn
            no-caps
            color="secondary"
            label="Return to Homepage"
            to="/teacher/homepage"
            icon="arrow_back"
          />
        </div>
      </div>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import { AVATAR_WHITELIST } from 'src/config/whitelist';

const router = useRouter();
const $q = useQuasar();

const openAvatarService = () => {
  window.open('https://avatar.hkbu.life', '_blank');
};

const fetchUserData = async () => {
  try {
    const response = await api.get('/user-info');
    const user = response.data;

    // Check if user is a teacher and in whitelist
    if (!user || user.role_name !== 'Teacher') {
      $q.notify({
        type: 'negative',
        message: 'Only teachers can access this feature',
      });
      await router.push('/login');
      return;
    }

    // Check whitelist
    if (!AVATAR_WHITELIST.includes(user.email)) {
      $q.notify({
        type: 'negative',
        message: 'You are not authorized to access this feature',
      });
      await router.push('/teacher/homepage');
      return;
    }

    // If all checks pass, automatically open avatar service in new window
    openAvatarService();
  } catch (error) {
    console.error('Failed to verify access:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to verify access',
    });
    await router.push('/login');
  }
};

onMounted(async () => {
  await fetchUserData();
});
</script>
