import os
import time
from langchain_openai import AzureChatOpenAI
from dotenv import load_dotenv

load_dotenv()
azure_openai_api_hkbulc1_key = os.environ.get("AZURE_OPENAI_API_HKBULC1_KEY")
azure_openai_api_hkbulc1_endpoint = os.environ.get("AZURE_OPENAI_API_HKBULC1_ENDPOINT")


def parse_azure_openai_api_res(session_id, chatbot_id, res, time_elapsed):
    # Prepare the record for database insertion
    try:
        # Extracting the data from the 'response_metadata' which is where the relevant information is located.
        response_metadata = res.response_metadata
        token_usage = response_metadata.get('token_usage', {})
        # Attempt to parse the response as if it's in the expected dictionary format.
        db_record = {
            "session_id": session_id,
            "chatbot_id": chatbot_id,
            "api_provider": "Azure OpenAI",
            "api_model": response_metadata.get("model_name", ""),
            "api_message_content": res.content,
            "api_message_role": "assistant",
            "api_finish_reason": response_metadata.get("finish_reason", ""),
            "api_prompt_tokens": token_usage.get("prompt_tokens", 0),
            "api_completion_tokens": token_usage.get("completion_tokens", 0),
            "api_total_tokens": token_usage.get("total_tokens", 0),
            "api_system_fingerprint": response_metadata.get("system_fingerprint", ""),
            "time_elapsed": time_elapsed
        }
    except AttributeError as e:
        print(f"AttributeError encountered: {e}. 'res' type: {type(res)}")
        # If the response is not in the expected dictionary format, return None.
        db_record = None
    except Exception as e:
        print(f"Exception encountered: {e}. 'res' type: {type(res)}")
        # If an unexpected exception occurs, return None.
        db_record = None

    return db_record


async def chat_by_azure_openai_api(conversation_list, model_name="gpt-35-turbo-16k", temperature=0.7):
    print("Use Azure OpenAI API")

    start_time = time.time()

    res = ""
    # Generate response by accessing Azure OpenAI API
    llm = AzureChatOpenAI(
        azure_endpoint=azure_openai_api_hkbulc1_endpoint,
        api_key=azure_openai_api_hkbulc1_key,
        openai_api_version="2024-02-01",
        azure_deployment=model_name,
        temperature=temperature,
    )

    res = await llm.ainvoke(conversation_list)
    chat_response = res.content

    end_time = time.time()
    time_elapsed = end_time - start_time
    print("Time elapsed:", time_elapsed, "seconds")

    return chat_response, res, time_elapsed
